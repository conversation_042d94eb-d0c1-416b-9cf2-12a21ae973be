#include "bno08x_bsp.h"
#include "main.h"

/* Global Variables */
volatile uint8_t bno08x_data_ready = 0;

/**
 * @brief  Initialize BNO08X BSP layer
 * @param  None
 * @retval None
 */
void bno08x_bsp_init(void)
{
    /* Reset BNO08X sensor */
    bno08x_bsp_reset();
    
    /* Clear data ready flag */
    bno08x_data_ready = 0;
}

/**
 * @brief  Hardware reset BNO08X sensor
 * @param  None
 * @retval BNO08X_Status_t
 */
BNO08X_Status_t bno08x_bsp_reset(void)
{
    /* Pull reset pin low */
    HAL_GPIO_WritePin(BNO_RST_GPIO_Port, BNO_RST_Pin, GPIO_PIN_RESET);
    
    /* Wait for reset timing */
    bno08x_bsp_delay_ms(BNO08X_RESET_DELAY_MS);
    
    /* Release reset pin */
    HAL_GPIO_WritePin(BNO_RST_GPIO_Port, BNO_RST_Pin, GPIO_PIN_SET);
    
    /* Wait for sensor to boot up */
    bno08x_bsp_delay_ms(100);
    
    return BNO08X_OK;
}

/**
 * @brief  I2C write function for BNO08X
 * @param  addr: I2C device address
 * @param  data: Data buffer to write
 * @param  len: Data length
 * @retval BNO08X_Status_t
 */
BNO08X_Status_t bno08x_bsp_i2c_write(uint8_t addr, uint8_t *data, uint16_t len)
{
    HAL_StatusTypeDef status;
    
    status = HAL_I2C_Master_Transmit(&hi2c1, addr << 1, data, len, 1000);
    
    if (status == HAL_OK) {
        return BNO08X_OK;
    } else if (status == HAL_TIMEOUT) {
        return BNO08X_TIMEOUT;
    } else if (status == HAL_BUSY) {
        return BNO08X_BUSY;
    } else {
        return BNO08X_ERROR;
    }
}

/**
 * @brief  I2C read function for BNO08X
 * @param  addr: I2C device address
 * @param  data: Data buffer to read
 * @param  len: Data length
 * @retval BNO08X_Status_t
 */
BNO08X_Status_t bno08x_bsp_i2c_read(uint8_t addr, uint8_t *data, uint16_t len)
{
    HAL_StatusTypeDef status;
    
    status = HAL_I2C_Master_Receive(&hi2c1, addr << 1, data, len, 1000);
    
    if (status == HAL_OK) {
        return BNO08X_OK;
    } else if (status == HAL_TIMEOUT) {
        return BNO08X_TIMEOUT;
    } else if (status == HAL_BUSY) {
        return BNO08X_BUSY;
    } else {
        return BNO08X_ERROR;
    }
}

/**
 * @brief  Get interrupt pin status
 * @param  None
 * @retval uint8_t: 1 if interrupt active, 0 if not
 */
uint8_t bno08x_bsp_get_int_status(void)
{
    return (HAL_GPIO_ReadPin(BNO_INT_GPIO_Port, BNO_INT_Pin) == GPIO_PIN_RESET) ? 1 : 0;
}

/**
 * @brief  Delay function in milliseconds
 * @param  ms: Delay time in milliseconds
 * @retval None
 */
void bno08x_bsp_delay_ms(uint32_t ms)
{
    HAL_Delay(ms);
}

/**
 * @brief  Initialize BNO08X sensor (high-level)
 * @param  None
 * @retval None
 */
void bno08x_init(void)
{
    /* Initialize BSP layer */
    bno08x_bsp_init();
    
    /* Check device presence */
    if (bno08x_check_device() == BNO08X_OK) {
        /* Device found, continue with initialization */
        /* TODO: Add BNO08X specific initialization here */
    }
}

/**
 * @brief  Process BNO08X data (high-level)
 * @param  None
 * @retval None
 */
void bno08x_proc(void)
{
    if (bno08x_data_ready) {
        /* Clear data ready flag */
        bno08x_data_ready = 0;
        
        /* TODO: Add BNO08X data processing here */
        /* Read sensor data via I2C */
        /* Parse and process the data */
    }
}

/**
 * @brief  Check if BNO08X device is present
 * @param  None
 * @retval BNO08X_Status_t
 */
BNO08X_Status_t bno08x_check_device(void)
{
    uint8_t test_data = 0;
    BNO08X_Status_t status;
    
    /* Try to read from device with default address */
    status = bno08x_bsp_i2c_read(BNO08X_I2C_ADDR_DEFAULT, &test_data, 1);
    
    if (status != BNO08X_OK) {
        /* Try alternative address */
        status = bno08x_bsp_i2c_read(BNO08X_I2C_ADDR_ALT, &test_data, 1);
    }
    
    return status;
}
