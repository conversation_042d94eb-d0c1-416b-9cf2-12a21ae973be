/**
 * @file mypid.c
 * @brief PID控制器实现文件
 * @details 包含位置式PID、增量式PID、积分分离PID、微分先行PID、角度PID等多种PID算法实现
 * <AUTHOR>
 * @date 2023
 */

#include "mypid.h"

// 全局PID参数配置
float maxout = 100;         // 最大输出限制
float i_out_limit = 500;    // 积分输出限制
float Kp;                   // 比例系数
float Ki;                   // 积分系数
float Kd;                   // 微分系数
float target,actual,pid_out; // 目标值、实际值、PID输出

// PID控制器实例定义
pid_t pid_x;    // X轴位置控制器
pid_t pid_y;    // Y轴位置控制器

pid_t pid_speed_left,pid_speed_right;       // 左右轮速度控制器
pid_t pid_location_left,pid_location_right; // 左右轮位置控制器

/**
 * @brief PID控制器初始化函数
 * @details 初始化所有PID控制器的参数，包括X/Y轴位置控制、左右轮速度控制和位置控制
 * @param 无
 * @retval 无
 */
void PID_INIT(void)
{
	/******************** PID参数初始化 ************************/
	// X/Y轴位置控制器初始化 - 位置式PID
	// 参数: 最大输出=3, 积分限制=1, Kp=0.02, Ki=0, Kd=0
	PID_struct_init(&pid_x ,POSITION_PID , 3, 1, 0.02, 0, 0);
	PID_struct_init(&pid_y ,POSITION_PID , 3, 1, 0.02, 0, 0);

	// 可选：设置输入死区，用于消除小幅抖动
	// pid_x.input_deadband = TOLERANCE_CM_X * PIXELS_PER_CM_X;
	// pid_y.input_deadband = TOLERANCE_CM_Y * PIXELS_PER_CM_Y;

	// 左右轮速度控制器初始化 - 增量式PID
	// 参数: 最大输出=200, 积分限制=50, Kp=0.5, Ki=0.2, Kd=0
	PID_struct_init(&pid_speed_left ,DELTA_PID , 200, 50, 0.5, 0.2, 0);
	PID_struct_init(&pid_speed_right ,DELTA_PID , 200, 50, 0.5, 0.2, 0);

	// 左右轮位置外环控制器初始化 - 位置式PID
	// 参数: 最大输出=40, 积分限制=0, Kp=0.1, Ki=0, Kd=0
	PID_struct_init(&pid_location_left ,POSITION_PID , 40, 0, 0.1, 0, 0);
	PID_struct_init(&pid_location_right ,POSITION_PID , 40, 0, 0.1, 0, 0);
}

/**
 * @brief 绝对值限幅函数
 * @details 将输入值限制在[-ABS_MAX, ABS_MAX]范围内，防止输出过大
 * @param[in,out] a 需要限幅的变量指针
 * @param[in] ABS_MAX 限幅的绝对值上限
 * @retval 无
 */
void abs_limit(float *a, float ABS_MAX)
{
  if (*a > ABS_MAX)
    *a = ABS_MAX;
  if (*a < -ABS_MAX)
    *a = -ABS_MAX;
}

/**
 * @brief PID参数初始化函数
 * @details 设置PID控制器的基本参数，包括控制模式、输出限制和PID系数
 * @param[in] pid PID控制器结构体指针
 * @param[in] mode PID控制模式 (POSITION_PID/DELTA_PID)
 * @param[in] maxout 最大输出限制
 * @param[in] intergral_limit 积分项输出限制
 * @param[in] kp 比例系数
 * @param[in] ki 积分系数
 * @param[in] kd 微分系数
 * @retval 无
 */
static void pid_param_init(
    pid_t *pid,
    uint32_t mode,
    uint32_t maxout,
    uint32_t intergral_limit,
    float kp,
    float ki,
    float kd)
{
  // 设置PID控制器基本参数
  pid->integral_limit = intergral_limit;  // 积分限制
  pid->max_out = maxout;                  // 最大输出
  pid->pid_mode = mode;                   // PID模式

  // 设置PID系数
  pid->p = kp;  // 比例系数
  pid->i = ki;  // 积分系数
  pid->d = kd;  // 微分系数
}

/**
 * @brief PID参数重置函数
 * @details 在程序运行时修改PID参数，并清零所有输出项
 * @param[in] pid PID控制器结构体指针
 * @param[in] kp 新的比例系数
 * @param[in] ki 新的积分系数
 * @param[in] kd 新的微分系数
 * @retval 无
 */
static void pid_reset(pid_t *pid, float kp, float ki, float kd)
{
  // 更新PID系数
  pid->p = kp;
  pid->i = ki;
  pid->d = kd;

  // 清零所有输出项，避免参数切换时的冲击
  pid->pout = 0;  // 比例输出清零
  pid->iout = 0;  // 积分输出清零
  pid->dout = 0;  // 微分输出清零
  pid->out = 0;   // 总输出清零
}

/**
 * @brief 标准PID计算函数
 * @details 计算增量式PID和位置式PID，支持平滑滤波和死区处理
 * @param[in] pid PID控制器结构体指针
 * @param[in] get 测量反馈值（当前值）
 * @param[in] set 目标设定值
 * @param[in] smoth 平滑滤波开关 (0=关闭, 1=开启)
 * @retval PID计算输出值
 */
float pid_calc(pid_t *pid, float get, float set ,uint8_t smoth)
{
	// 保存当前测量值和目标值
	pid->get = get;
	pid->set = set;
	
	// 注释掉的原始平滑滤波代码
	// if(smoth==0){
	// pid->err[NOW] = set - get;}
	// else{
	//		float   pid_err;
	//		pid_err= set - get;
	//		pid->err[NOW] = pid_err*0.7+ pid->err[LAST]*0.3;
	// }

    // 先计算原始误差
    float raw_error = set - get;

    // 应用输入死区处理
    // 如果原始误差的绝对值小于或等于 input_deadband，则误差设为0
    if (pid->input_deadband != 0 && fabs(raw_error) <= pid->input_deadband)
    {
        pid->err[NOW] = 0;
        // 在死区范围内，通常需要清零积分项以防止积分饱和（integral wind-up）
        pid->iout = 0; // 直接清零积分项
    }
    else
    {
        // 不在死区范围内，根据 smoth 参数计算当前误差
        if (smoth == 0)
        {
            // 不使用平滑滤波，直接使用原始误差
            pid->err[NOW] = raw_error;
        }
        else
        {
            // 使用平滑滤波：当前误差 = 0.7*当前原始误差 + 0.3*上次误差
            // 这种滤波可以减少噪声影响，但会增加系统延迟
            pid->err[NOW] = raw_error * 0.7f + pid->err[LAST] * 0.3f;
        }
    }
 	
  // 检查输入误差是否超过最大允许误差
  if ((pid->input_max_err != 0) && (fabs(pid->err[NOW]) > pid->input_max_err))
    return 0;

  if (pid->pid_mode == POSITION_PID) // 位置式PID计算
  {
    // 比例项：P = Kp * e(k)
    pid->pout = pid->p * pid->err[NOW];

    // 积分项：I = Ki * ∑e(k)，累加当前误差
    pid->iout += pid->i * pid->err[NOW];
    abs_limit(&(pid->iout), pid->integral_limit);	// 积分限幅

    // 微分项：D = Kd * [e(k) - e(k-1)]
    pid->dout = pid->d * (pid->err[NOW] - pid->err[LAST]);

    // 位置式PID输出：u(k) = P + I + D
    pid->out = pid->pout + pid->iout + pid->dout;
    abs_limit(&(pid->out), pid->max_out);  // 输出限幅
  }
  else if (pid->pid_mode == DELTA_PID) // 增量式PID计算
  {
    // 比例项：P = Kp * [e(k) - e(k-1)]
    pid->pout = pid->p * (pid->err[NOW] - pid->err[LAST]);
    // 积分项：I = Ki * e(k)
    pid->iout = pid->i * pid->err[NOW];
    // 微分项：D = Kd * [e(k) - 2*e(k-1) + e(k-2)]
    pid->dout = pid->d * (pid->err[NOW] - 2 * pid->err[LAST] + pid->err[LLAST]);

    // 增量式PID输出：Δu(k) = P + I + D，累加到总输出
    pid->out += pid->pout + pid->iout + pid->dout;
    abs_limit(&(pid->out), pid->max_out);  // 输出限幅
  }

	// 更新误差历史记录
	pid->err[LLAST] = pid->err[LAST];  // 前前次误差
	pid->err[LAST] = pid->err[NOW];    // 前次误差

  // 输出死区处理：如果输出小于死区阈值，则输出为0
  if ((pid->output_deadband != 0) && (fabs(pid->out) < pid->output_deadband))
    return 0;
  else
    return pid->out;
}

/**
 * @brief 积分分离PID计算函数
 * @details 当误差较大时，取消积分作用，避免积分饱和；误差较小时，恢复积分作用
 * @param[in] pid PID控制器结构体指针
 * @param[in] get 测量反馈值
 * @param[in] set 目标设定值
 * @param[in] smoth 平滑滤波开关 (0=关闭, 1=开启)
 * @param[in] i_separationThreshold 积分分离阈值
 * @retval PID计算输出值
 */
float pid_calc_i_separation(pid_t *pid, float get, float set ,uint8_t smoth,float i_separationThreshold)
{
	// 保存当前测量值和目标值
	pid->get = get;
	pid->set = set;

	// 计算当前误差，支持平滑滤波
	if(smoth==0){
		pid->err[NOW] = set - get;
	}
	else{
		float   pid_err;
		pid_err= set - get;
		pid->err[NOW] = pid_err*0.7f+ pid->err[LAST]*0.3f;
	}

  // 检查输入误差是否超过最大允许误差
  if ((pid->input_max_err != 0) && (fabs(pid->err[NOW]) > pid->input_max_err))
    return 0;

  if (pid->pid_mode == POSITION_PID) // 位置式PID
  {
    // 比例项计算
    pid->pout = pid->p * pid->err[NOW];

		// 积分分离算法核心
		uint8_t i_separation_index=1;  // 积分项使能标志
		if(fabs(pid->err[NOW]) >= i_separationThreshold)
		{
			// 误差较大时，取消积分作用，避免积分饱和
			i_separation_index=0;

			// 方法1：积分衰减（可选）
			// 系数越大 → 积分项衰减越快
			// 系数越小 → 更平滑，但积分项衰减较慢
			// pid->iout *= 0.95; // 大误差时，每次都衰减

			// 方法2：符号变化复位（可选）
			// if (pid->err[NOW] * pid->err[LAST] < 0)
			// {
			//     pid->iout *= 0.6;  // 过零时积分衰减
			// }
		}
		else
		{
			// 误差较小时，恢复积分作用
			float ramp_factor = 1.0;
			// 可选：渐进恢复积分
			// if (fabs(pid->err[NOW]) > i_separationThreshold * 0.7)
			// {
			//     ramp_factor = (i_separationThreshold - fabs(pid->err[NOW])) / (0.3 * i_separationThreshold);
			// }

			i_separation_index=1;
			pid->iout += ramp_factor * pid->i * pid->err[NOW];
		}

		// 积分限幅：防止积分项过大
		abs_limit(&(pid->iout), pid->integral_limit);

    // 微分项计算
    pid->dout = pid->d * (pid->err[NOW] - pid->err[LAST]);

    // PID输出：P + I*分离标志 + D
    pid->out = pid->pout + i_separation_index*pid->iout + pid->dout;
    abs_limit(&(pid->out), pid->max_out);
  }
  else if (pid->pid_mode == DELTA_PID) // 增量式PID
  {
    // 比例项计算
    pid->pout = pid->p * (pid->err[NOW] - pid->err[LAST]);

		// 积分分离处理
		uint8_t i_separation_index=1;
		if(fabs(pid->err[NOW]) >= i_separationThreshold)
		{
			i_separation_index=0;  // 大误差时取消积分
		}
		else
		{
			i_separation_index=1;  // 小误差时恢复积分
			pid->iout = pid->i * pid->err[NOW];
		}

    // 微分项计算
    pid->dout = pid->d * (pid->err[NOW] - 2 * pid->err[LAST] + pid->err[LLAST]);

    // 增量式PID输出
    pid->out += pid->pout + i_separation_index*pid->iout + pid->dout;
    abs_limit(&(pid->out), pid->max_out);
  }

	// 更新误差历史
	pid->err[LLAST] = pid->err[LAST];
	pid->err[LAST] = pid->err[NOW];

  // 输出死区处理
  if ((pid->output_deadband != 0) && (fabs(pid->out) < pid->output_deadband))
    return 0;
  else
    return pid->out;
}

/**
 * @brief 微分先行PID计算函数
 * @details 使用实际输出值的微分代替误差微分，减少微分项的噪声影响
 * @param[in] pid PID控制器结构体指针
 * @param[in] get 测量反馈值
 * @param[in] set 目标设定值
 * @param[in] actual 当前实际输出值
 * @param[in] last_actual 上次实际输出值
 * @param[in] smoth 平滑滤波开关 (0=关闭, 1=开启)
 * @retval PID计算输出值
 */
float pid_calc_d(pid_t *pid, float get, float set ,float actual,float last_actual,uint8_t smoth)
{
	// 保存当前测量值和目标值
	pid->get = get;
	pid->set = set;

	// 计算当前误差，支持平滑滤波
	if(smoth==0){
		pid->err[NOW] = set - get;
	}
	else{
		float   pid_err;
		pid_err= set - get;
		pid->err[NOW] = pid_err*0.7f+ pid->err[LAST]*0.3f;
	}

  // 检查输入误差是否超过最大允许误差
  if ((pid->input_max_err != 0) && (fabs(pid->err[NOW]) > pid->input_max_err))
    return 0;

  if (pid->pid_mode == POSITION_PID) // 位置式PID
  {
    // 比例项：P = Kp * e(k)
    pid->pout = pid->p * pid->err[NOW];

    // 积分项：I = Ki * ∑e(k)
    pid->iout += pid->i * pid->err[NOW];
    abs_limit(&(pid->iout), pid->integral_limit);	// 积分限幅

		// 微分先行：D = -Kd * [y(k) - y(k-1)]
		// 使用实际输出值的微分代替误差微分，减少噪声影响
		// 这种方法可以避免设定值突变时产生的微分冲击
		pid->dout = - pid->d * (actual - last_actual);

    // PID输出：u(k) = P + I + D
    pid->out = pid->pout + pid->iout + pid->dout;
    abs_limit(&(pid->out), pid->max_out);  // 输出限幅
  }

	// 更新误差历史
	pid->err[LLAST] = pid->err[LAST];
	pid->err[LAST] = pid->err[NOW];

  // 输出死区处理
  if ((pid->output_deadband != 0) && (fabs(pid->out) < pid->output_deadband))
    return 0;
  else
    return pid->out;
}

/**
 * @brief 角度PID计算函数 (0~360度)
 * @details 专门用于角度控制的PID，处理角度的周期性特性，确保最短路径控制
 * @param[in] pid PID控制器结构体指针
 * @param[in] get 当前角度值 (0~360度)
 * @param[in] set 目标角度值 (0~360度)
 * @param[in] smoth 平滑滤波开关 (0=关闭, 1=开启)
 * @retval PID计算输出值
 */
float pid_angle_calc(pid_t *pid, float get, float set ,uint8_t smoth)
{
	// 保存当前测量值和目标值
	pid->get = get;
	pid->set = set;

	// 计算当前误差，支持平滑滤波
	if(smoth==0){
		pid->err[NOW] = set - get;
	}
	else{
		float   pid_err;
		pid_err= set - get;
		pid->err[NOW] = pid_err*0.7f+ pid->err[LAST]*0.3f;
	}

  // 检查输入误差是否超过最大允许误差
  if ((pid->input_max_err != 0) && (fabs(pid->err[NOW]) > pid->input_max_err))
    return 0;

  if (pid->pid_mode == POSITION_PID) // 位置式PID
  {
		// 角度误差归一化处理，确保误差在[-180, 180]范围内
		// 这样可以保证角度控制走最短路径
		float e = pid->err[NOW];
		e = fmod(e + 180.0f, 360.0f);  // 将误差映射到[0, 360)
		if (e < 0) e += 360.0f;        // 处理负数情况
		pid->err[NOW] = e - 180.0f;    // 最终映射到[-180, 180]

    // 比例项计算
    pid->pout = pid->p * pid->err[NOW];

    // 积分项计算
    pid->iout += pid->i * pid->err[NOW];
    abs_limit(&(pid->iout), pid->integral_limit);

    // 微分项计算
    pid->dout = pid->d * (pid->err[NOW] - pid->err[LAST]);

    // PID输出
    pid->out = pid->pout + pid->iout + pid->dout;
    abs_limit(&(pid->out), pid->max_out);
  }

	// 更新误差历史
	pid->err[LLAST] = pid->err[LAST];
	pid->err[LAST] = pid->err[NOW];

  // 输出死区处理
  if ((pid->output_deadband != 0) && (fabs(pid->out) < pid->output_deadband))
    return 0;
  else
    return pid->out;
}

/**
 * @brief Yaw角度PID计算函数 (-180~180度)
 * @details 专门用于Yaw轴角度控制的PID，处理-180到180度范围的角度控制
 * @param[in] pid PID控制器结构体指针
 * @param[in] get 当前Yaw角度值 (-180~180度)
 * @param[in] set 目标Yaw角度值 (-180~180度)
 * @param[in] smoth 平滑滤波开关 (0=关闭, 1=开启)
 * @retval PID计算输出值
 */
float pid_yaw_calc(pid_t *pid, float get, float set ,uint8_t smoth)
{
	// 保存当前测量值和目标值
	pid->get = get;
	pid->set = set;

	// 计算当前误差，支持平滑滤波
	if(smoth==0)
	{
		pid->err[NOW] = set - get;
	}
	else
	{
		float   pid_err;
		pid_err= set - get;
		pid->err[NOW] = pid_err*0.7f+ pid->err[LAST]*0.3f;
	}

  // 检查输入误差是否超过最大允许误差
  if ((pid->input_max_err != 0) && (fabs(pid->err[NOW]) > pid->input_max_err))
    return 0;

	// Yaw角度误差限制在[-180, 180]范围内，确保最短路径控制
	if(pid->err[NOW]>180) {
		pid->err[NOW] = -(360-pid->err[NOW]);  // 大于180度时转换为负角度
	}
	else if(pid->err[NOW]<-180) {
		pid->err[NOW] = 360+pid->err[NOW];     // 小于-180度时转换为正角度
	}

  if (pid->pid_mode == POSITION_PID) // 位置式PID
  {
    // 比例项计算
    pid->pout = pid->p * pid->err[NOW];

    // 积分项计算
    pid->iout += pid->i * pid->err[NOW];
    abs_limit(&(pid->iout), pid->integral_limit);

    // 微分项计算
    pid->dout = pid->d * (pid->err[NOW] - pid->err[LAST]);

    // PID输出
    pid->out = pid->pout + pid->iout + pid->dout;
    abs_limit(&(pid->out), pid->max_out);
  }

	// 更新误差历史
	pid->err[LLAST] = pid->err[LAST];
	pid->err[LAST] = pid->err[NOW];

  // 输出死区处理
  if ((pid->output_deadband != 0) && (fabs(pid->out) < pid->output_deadband))
    return 0;
  else
    return pid->out;
}

/**
 * @brief PID输出清零函数
 * @details 清零PID控制器的所有输出项，用于重置或初始化
 * @param[in] pid PID控制器结构体指针
 * @retval 无
 */
void pid_clear(pid_t *pid)
{
  pid->pout = 0;  // 比例输出清零
  pid->iout = 0;  // 积分输出清零
  pid->dout = 0;  // 微分输出清零
  pid->out = 0;   // 总输出清零
}

/**
 * @brief PID结构体初始化函数
 * @details 初始化PID控制器结构体，设置函数指针和参数
 * @param[in] pid PID控制器结构体指针
 * @param[in] mode PID控制模式 (POSITION_PID/DELTA_PID)
 * @param[in] maxout 最大输出限制
 * @param[in] intergral_limit 积分项输出限制
 * @param[in] kp 比例系数
 * @param[in] ki 积分系数
 * @param[in] kd 微分系数
 * @retval 无
 */
void PID_struct_init(
    pid_t *pid,
    uint32_t mode,
    uint32_t maxout,
    uint32_t intergral_limit,
    float kp,
    float ki,
    float kd)
{
  // 设置函数指针，实现面向对象的编程风格
  pid->f_param_init = pid_param_init;  // 参数初始化函数指针
  pid->f_pid_reset = pid_reset;        // 参数重置函数指针

  // 调用参数初始化函数
  pid->f_param_init(pid, mode, maxout, intergral_limit, kp, ki, kd);
  // 调用参数重置函数，清零输出项
  pid->f_pid_reset(pid, kp, ki, kd);
}

/*
 * 注释掉的原始位置式PID函数，保留作为参考
 *
 * float position_pid_calc(pid_t *pid, float get, float set)
 * {
 *   pid->get = get;
 *   pid->set = set;
 *   pid->err[NOW] = set - get;
 *   pid->pout = pid->p * pid->err[NOW];
 *   pid->iout += pid->i * pid->err[NOW];
 *   pid->dout = pid->d * (pid->err[NOW] - pid->err[LAST]);
 *   abs_limit(&(pid->iout), pid->integral_limit);
 *   pid->out = pid->pout + pid->iout + pid->dout;
 *   abs_limit(&(pid->out), pid->max_out);
 *   pid->err[LLAST] = pid->err[LAST];
 *   pid->err[LAST] = pid->err[NOW];
 *   return pid->out;
 * }
 */
