Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.EXTI3_IRQHandler) for EXTI3_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler) for DMA1_Stream0_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.TIM2_IRQHandler) for TIM2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UART5_IRQHandler) for UART5_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) for DMA2_Stream2_IRQHandler
    startup_stm32f407xx.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(HEAP) for Heap_Mem
    startup_stm32f407xx.o(.text) refers to startup_stm32f407xx.o(STACK) for Stack_Mem
    main.o(i.SystemClock_Config) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to main.o(i.Error_Handler) for Error_Handler
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C1_Init) for MX_I2C1_Init
    main.o(i.main) refers to tim.o(i.MX_TIM3_Init) for MX_TIM3_Init
    main.o(i.main) refers to tim.o(i.MX_TIM4_Init) for MX_TIM4_Init
    main.o(i.main) refers to i2c.o(i.MX_I2C2_Init) for MX_I2C2_Init
    main.o(i.main) refers to tim.o(i.MX_TIM1_Init) for MX_TIM1_Init
    main.o(i.main) refers to usart.o(i.MX_UART5_Init) for MX_UART5_Init
    main.o(i.main) refers to tim.o(i.MX_TIM2_Init) for MX_TIM2_Init
    main.o(i.main) refers to my_scheduler.o(i.all_task_init) for all_task_init
    main.o(i.main) refers to my_scheduler.o(i.all_task_run) for all_task_run
    gpio.o(i.MX_GPIO_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    gpio.o(i.MX_GPIO_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    i2c.o(i.HAL_I2C_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    i2c.o(i.HAL_I2C_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    i2c.o(i.HAL_I2C_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    i2c.o(i.MX_I2C1_Init) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C1_Init) refers to i2c.o(.bss) for hi2c1
    i2c.o(i.MX_I2C2_Init) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) for HAL_I2C_Init
    i2c.o(i.MX_I2C2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    i2c.o(i.MX_I2C2_Init) refers to i2c.o(.bss) for hi2c2
    tim.o(i.HAL_TIM_Base_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    tim.o(i.HAL_TIM_Base_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    tim.o(i.HAL_TIM_Encoder_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_Encoder_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.HAL_TIM_MspPostInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.HAL_TIM_MspPostInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    tim.o(i.MX_TIM1_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM1_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) for HAL_TIM_PWM_Init
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) for HAL_TIM_PWM_ConfigChannel
    tim.o(i.MX_TIM1_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime) for HAL_TIMEx_ConfigBreakDeadTime
    tim.o(i.MX_TIM1_Init) refers to tim.o(i.HAL_TIM_MspPostInit) for HAL_TIM_MspPostInit
    tim.o(i.MX_TIM1_Init) refers to tim.o(.bss) for htim1
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) for HAL_TIM_Base_Init
    tim.o(i.MX_TIM2_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) for HAL_TIM_ConfigClockSource
    tim.o(i.MX_TIM2_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM2_Init) refers to tim.o(.bss) for htim2
    tim.o(i.MX_TIM3_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM3_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM3_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM3_Init) refers to tim.o(.bss) for htim3
    tim.o(i.MX_TIM4_Init) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) for HAL_TIM_Encoder_Init
    tim.o(i.MX_TIM4_Init) refers to main.o(i.Error_Handler) for Error_Handler
    tim.o(i.MX_TIM4_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization) for HAL_TIMEx_MasterConfigSynchronization
    tim.o(i.MX_TIM4_Init) refers to tim.o(.bss) for htim4
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for hdma_uart5_rx
    usart.o(i.MX_UART5_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_UART5_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_UART5_Init) refers to usart.o(.bss) for huart5
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for huart1
    stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler) refers to usart.o(.bss) for hdma_uart5_rx
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to usart.o(.bss) for hdma_usart1_rx
    stm32f4xx_it.o(i.EXTI3_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) for HAL_GPIO_EXTI_IRQHandler
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(i.TIM2_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) for HAL_TIM_IRQHandler
    stm32f4xx_it.o(i.TIM2_IRQHandler) refers to tim.o(.bss) for htim2
    stm32f4xx_it.o(i.UART5_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.UART5_IRQHandler) refers to usart.o(.bss) for huart5
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f4xx_hal_msp.o(i.HAL_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit) refers to i2c.o(i.HAL_I2C_MspDeInit) for HAL_I2C_MspDeInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) for I2C_Slave_AF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions) for I2C_ConvertOtherXferOptions
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_SB) for I2C_Master_SB
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_ADD10) for I2C_Master_ADD10
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR) for I2C_Master_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) for I2C_MasterTransmit_TXE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) for I2C_MasterTransmit_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) for I2C_MasterReceive_RXNE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) for I2C_MasterReceive_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) for I2C_Slave_ADDR
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) for I2C_Slave_STOPF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_SlaveTransmit_TXE) for I2C_SlaveTransmit_TXE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_SlaveTransmit_BTF) for I2C_SlaveTransmit_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_SlaveReceive_RXNE) for I2C_SlaveReceive_RXNE
    stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler) refers to stm32f4xx_hal_i2c.o(i.I2C_SlaveReceive_BTF) for I2C_SlaveReceive_BTF
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to i2c.o(i.HAL_I2C_MspInit) for HAL_I2C_MspInit
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Init) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) for I2C_MasterRequestRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) for I2C_MasterRequestWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) for I2C_RequestMemoryRead
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) for I2C_WaitOnBTFFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) for I2C_RequestMemoryWrite
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) for I2C_WaitOnRXNEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) for I2C_WaitOnSTOPFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) for I2C_DMAXferCplt
    stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAError) for I2C_DMAError
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_i2c.o(i.I2C_DMAError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback) for HAL_I2C_AbortCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_ITError) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPRequestThroughIT) for I2C_WaitOnSTOPRequestThroughIT
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback) for HAL_I2C_MemRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback) for HAL_I2C_MasterRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback) for HAL_I2C_ErrorCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback) for HAL_I2C_MasterTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) for I2C_MemoryTransmit_TXE_BTF
    stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback) for HAL_I2C_MemTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF) refers to stm32f4xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) for I2C_WaitOnFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) for I2C_WaitOnMasterAddressFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite) refers to stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) for I2C_WaitOnTXEFlagUntilTimeout
    stm32f4xx_hal_i2c.o(i.I2C_SlaveReceive_RXNE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_SlaveTransmit_TXE) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback) for HAL_I2C_AddrCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.I2C_Flush_DR) for I2C_Flush_DR
    stm32f4xx_hal_i2c.o(i.I2C_Slave_AF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback) for HAL_I2C_SlaveTxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetState) for HAL_DMA_GetState
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_ITError) for I2C_ITError
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback) for HAL_I2C_SlaveRxCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback) for HAL_I2C_ListenCpltCallback
    stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF) refers to stm32f4xx_hal_i2c.o(i.I2C_DMAAbort) for I2C_DMAAbort
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPRequestThroughIT) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed) for I2C_IsAcknowledgeFailed
    stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetUser) for FLASH_OB_GetUser
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetBOR) for FLASH_OB_GetBOR
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BOR_LevelConfig) for FLASH_OB_BOR_LevelConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for flagBitshiftOffset
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping) for __NVIC_GetPriorityGrouping
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping) for __NVIC_GetPriorityGrouping
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for uwTickFreq
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for uwTick
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for uwTickFreq
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for uwTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for uwTickFreq
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for uwTickFreq
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit) refers to tim.o(i.HAL_TIM_Base_MspDeInit) for HAL_TIM_Base_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to tim.o(i.HAL_TIM_Base_MspInit) for HAL_TIM_Base_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig) for TIM_ITRx_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) for TIM_DMAPeriodElapsedCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) for TIM_DMAPeriodElapsedHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) for TIM_DMATriggerCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) refers to stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) for TIM_DMATriggerHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart) for HAL_TIM_DMABurst_MultiReadStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart) for HAL_TIM_DMABurst_MultiWriteStart
    stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit) refers to tim.o(i.HAL_TIM_Encoder_MspDeInit) for HAL_TIM_Encoder_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to tim.o(i.HAL_TIM_Encoder_MspInit) for HAL_TIM_Encoder_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI3_SetConfig) for TIM_TI3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI4_SetConfig) for TIM_TI4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit) for HAL_TIM_IC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit) for HAL_TIM_IC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback) for HAL_TIM_OC_DelayElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to my_timer.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback) for HAL_TIMEx_BreakCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit) for HAL_TIM_OC_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit) for HAL_TIM_OC_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig) for TIM_TI2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit) for HAL_TIM_OnePulse_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit) for HAL_TIM_OnePulse_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig) for TIM_OC1_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig) for TIM_OC3_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel) refers to stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig) for TIM_OC4_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit) for HAL_TIM_PWM_MspDeInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit) for HAL_TIM_PWM_MspInit
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) for TIM_DMADelayPulseCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT) refers to stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) for TIM_SlaveTimer_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback) for HAL_TIM_IC_CaptureCallback
    stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback) for HAL_TIM_IC_CaptureHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback) for HAL_TIM_PWM_PulseFinishedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAError) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt) refers to my_timer.o(i.HAL_TIM_PeriodElapsedCallback) for HAL_TIM_PeriodElapsedCallback
    stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback) for HAL_TIM_PeriodElapsedHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback) for HAL_TIM_TriggerCallback
    stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback) for HAL_TIM_TriggerHalfCpltCallback
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig) for TIM_ETR_SetConfig
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage) for TIM_TI1_ConfigInputStage
    stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig) refers to stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage) for TIM_TI2_ConfigInputStage
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) for TIMEx_DMACommutationCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) for TIMEx_DMACommutationHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit) for HAL_TIMEx_HallSensor_MspDeInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit) for HAL_TIMEx_HallSensor_MspInit
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig) for TIM_Base_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig) for TIM_TI1_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init) refers to stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig) for TIM_OC2_SetConfig
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt) for TIM_DMACaptureCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt) for TIM_DMACaptureHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMAError) for TIM_DMAError
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT) refers to stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd) for TIM_CCxChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) for TIM_DMADelayPulseNCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt) for TIM_DMADelayPulseHalfCplt
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) for TIM_DMAErrorCCxN
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT) refers to stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd) for TIM_CCxNChannelCmd
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback) for HAL_TIMEx_CommutCallback
    stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt) refers to stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback) for HAL_TIMEx_CommutHalfCpltCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback) for HAL_TIM_PWM_PulseFinishedCallback
    stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback) for HAL_TIM_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to usart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Transmit_IT) for UART_Transmit_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT) for UART_EndTransmit_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) for UART_Start_Receive_DMA
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT) for UART_Start_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to usart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to usart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to usart_app.o(i.HAL_UARTEx_RxEventCallback) for HAL_UARTEx_RxEventCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to lludivv7m.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    ringbuffer.o(i.rt_ringbuffer_data_len) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    ringbuffer.o(i.rt_ringbuffer_get) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_get) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_get) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_getchar) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_getchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_init) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_peek) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_peek) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_put) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_put_force) refers to rt_memcpy_v6.o(.text) for __aeabi_memcpy
    ringbuffer.o(i.rt_ringbuffer_putchar) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_putchar) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    ringbuffer.o(i.rt_ringbuffer_putchar_force) refers to assert.o(.text) for __aeabi_assert
    ringbuffer.o(i.rt_ringbuffer_putchar_force) refers to ringbuffer.o(i.rt_ringbuffer_status) for rt_ringbuffer_status
    ringbuffer.o(i.rt_ringbuffer_reset) refers to assert.o(.text) for __aeabi_assert
    software_iic.o(i.Delay_us) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    software_iic.o(i.IIC_Anolog_Normalize) refers to software_iic.o(i.IIC_WriteBytes) for IIC_WriteBytes
    software_iic.o(i.IIC_Delay) refers to software_iic.o(i.Delay_us) for Delay_us
    software_iic.o(i.IIC_Get_Anolog) refers to software_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    software_iic.o(i.IIC_Get_Digtal) refers to software_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    software_iic.o(i.IIC_Get_Offset) refers to software_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    software_iic.o(i.IIC_Get_Single_Anolog) refers to software_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    software_iic.o(i.IIC_ReadByte) refers to software_iic.o(i.IIC_Start) for IIC_Start
    software_iic.o(i.IIC_ReadByte) refers to software_iic.o(i.IIC_SendByte) for IIC_SendByte
    software_iic.o(i.IIC_ReadByte) refers to software_iic.o(i.IIC_RecvByte) for IIC_RecvByte
    software_iic.o(i.IIC_ReadByte) refers to software_iic.o(i.IIC_SendNAck) for IIC_SendNAck
    software_iic.o(i.IIC_ReadByte) refers to software_iic.o(i.IIC_Stop) for IIC_Stop
    software_iic.o(i.IIC_ReadBytes) refers to software_iic.o(i.IIC_Start) for IIC_Start
    software_iic.o(i.IIC_ReadBytes) refers to software_iic.o(i.IIC_SendByte) for IIC_SendByte
    software_iic.o(i.IIC_ReadBytes) refers to software_iic.o(i.IIC_Stop) for IIC_Stop
    software_iic.o(i.IIC_ReadBytes) refers to software_iic.o(i.IIC_RecvByte) for IIC_RecvByte
    software_iic.o(i.IIC_ReadBytes) refers to software_iic.o(i.IIC_SendNAck) for IIC_SendNAck
    software_iic.o(i.IIC_ReadBytes) refers to software_iic.o(i.IIC_SendAck) for IIC_SendAck
    software_iic.o(i.IIC_RecvByte) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    software_iic.o(i.IIC_RecvByte) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    software_iic.o(i.IIC_RecvByte) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    software_iic.o(i.IIC_RecvByte) refers to software_iic.o(i.IIC_Delay) for IIC_Delay
    software_iic.o(i.IIC_RecvByte) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    software_iic.o(i.IIC_SendAck) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    software_iic.o(i.IIC_SendAck) refers to software_iic.o(i.IIC_Delay) for IIC_Delay
    software_iic.o(i.IIC_SendByte) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    software_iic.o(i.IIC_SendByte) refers to software_iic.o(i.IIC_Delay) for IIC_Delay
    software_iic.o(i.IIC_SendByte) refers to software_iic.o(i.IIC_WaitAck) for IIC_WaitAck
    software_iic.o(i.IIC_SendNAck) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    software_iic.o(i.IIC_SendNAck) refers to software_iic.o(i.IIC_Delay) for IIC_Delay
    software_iic.o(i.IIC_Start) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    software_iic.o(i.IIC_Start) refers to software_iic.o(i.IIC_Delay) for IIC_Delay
    software_iic.o(i.IIC_Stop) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    software_iic.o(i.IIC_Stop) refers to software_iic.o(i.IIC_Delay) for IIC_Delay
    software_iic.o(i.IIC_WaitAck) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    software_iic.o(i.IIC_WaitAck) refers to software_iic.o(i.IIC_Delay) for IIC_Delay
    software_iic.o(i.IIC_WaitAck) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    software_iic.o(i.IIC_WriteByte) refers to software_iic.o(i.IIC_Start) for IIC_Start
    software_iic.o(i.IIC_WriteByte) refers to software_iic.o(i.IIC_SendByte) for IIC_SendByte
    software_iic.o(i.IIC_WriteByte) refers to software_iic.o(i.IIC_Stop) for IIC_Stop
    software_iic.o(i.IIC_WriteBytes) refers to software_iic.o(i.IIC_Start) for IIC_Start
    software_iic.o(i.IIC_WriteBytes) refers to software_iic.o(i.IIC_SendByte) for IIC_SendByte
    software_iic.o(i.IIC_WriteBytes) refers to software_iic.o(i.IIC_Stop) for IIC_Stop
    software_iic.o(i.Ping) refers to software_iic.o(i.IIC_ReadBytes) for IIC_ReadBytes
    pid.o(i.pid_calculate_incremental) refers to pid.o(i.pid_formula_incremental) for pid_formula_incremental
    pid.o(i.pid_calculate_incremental) refers to pid.o(i.pid_out_limit) for pid_out_limit
    pid.o(i.pid_calculate_positional) refers to pid.o(i.pid_formula_positional) for pid_formula_positional
    pid.o(i.pid_calculate_positional) refers to pid.o(i.pid_out_limit) for pid_out_limit
    motor_driver.o(i.DRV8871_Control) refers to motor_driver.o(i.Speed1000_To_PWM) for Speed1000_To_PWM
    motor_driver.o(i.DRV8871_Control) refers to motor_driver.o(i.Set_Pin_Mode) for Set_Pin_Mode
    motor_driver.o(i.Float_To_Speed1000) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    motor_driver.o(i.Motor_Create) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start) for HAL_TIM_PWM_Start
    motor_driver.o(i.Motor_Create) refers to motor_driver.o(i.DRV8871_Control) for DRV8871_Control
    motor_driver.o(i.Motor_Enable) refers to motor_driver.o(i.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver.o(i.Motor_Enable) refers to motor_driver.o(i.DRV8871_Control) for DRV8871_Control
    motor_driver.o(i.Motor_GetState) refers to motor_driver.o(i.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver.o(i.Motor_SetDecayMode) refers to motor_driver.o(i.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver.o(i.Motor_SetDecayMode) refers to motor_driver.o(i.Motor_SetSpeed) for Motor_SetSpeed
    motor_driver.o(i.Motor_SetSpeed) refers to motor_driver.o(i.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver.o(i.Motor_SetSpeed) refers to motor_driver.o(i.Motor_ValidateFloatSpeed) for Motor_ValidateFloatSpeed
    motor_driver.o(i.Motor_SetSpeed) refers to motor_driver.o(i.Float_To_Speed1000) for Float_To_Speed1000
    motor_driver.o(i.Motor_SetSpeed) refers to motor_driver.o(i.DRV8871_Control) for DRV8871_Control
    motor_driver.o(i.Motor_Stop) refers to motor_driver.o(i.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver.o(i.Motor_Stop) refers to motor_driver.o(i.DRV8871_Control) for DRV8871_Control
    motor_driver.o(i.Set_Pin_Mode) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    motor_driver.o(i.Set_Pin_Mode) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    motor_driver.o(i.Set_Pin_Mode) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    oled.o(i.OLED_Allfill) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Allfill) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Clear) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_Display_Off) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Display_On) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Clear) for OLED_Clear
    oled.o(i.OLED_Init) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_Init) refers to oled.o(.data) for initcmd1
    oled.o(i.OLED_Set_Position) refers to oled.o(i.OLED_Write_cmd) for OLED_Write_cmd
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowChar) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowChar) refers to oled.o(.constdata) for F8X16
    oled.o(i.OLED_ShowFloat) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowHanzi) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowHanzi) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowHanzi) refers to oled.o(.constdata) for Hzk
    oled.o(i.OLED_ShowHzbig) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowHzbig) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowHzbig) refers to oled.o(.constdata) for Hzb
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_Pow) for OLED_Pow
    oled.o(i.OLED_ShowNum) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_ShowPic) refers to oled.o(i.OLED_Set_Position) for OLED_Set_Position
    oled.o(i.OLED_ShowPic) refers to oled.o(i.OLED_Write_data) for OLED_Write_data
    oled.o(i.OLED_ShowStr) refers to oled.o(i.OLED_ShowChar) for OLED_ShowChar
    oled.o(i.OLED_Write_cmd) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(i.OLED_Write_cmd) refers to i2c.o(.bss) for hi2c2
    oled.o(i.OLED_Write_data) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write) for HAL_I2C_Mem_Write
    oled.o(i.OLED_Write_data) refers to i2c.o(.bss) for hi2c2
    encoder_driver.o(i.Encoder_Driver_Init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start) for HAL_TIM_Encoder_Start
    encoder_driver.o(i.Encoder_Driver_Update) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    encoder_driver.o(i.Encoder_Driver_Update) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    encoder_driver.o(i.Encoder_Driver_Update) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    bno08x_hal.o(i.BNO080_HardwareReset) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    bno08x_hal.o(i.BNO080_HardwareReset) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    bno08x_hal.o(i.BNO080_HardwareReset) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    bno08x_hal.o(i.BNO080_HardwareReset) refers to bno08x_hal.o(i.receivePacket) for receivePacket
    bno08x_hal.o(i.BNO080_HardwareReset) refers to bno08x_hal.o(.bss) for shtpData
    bno08x_hal.o(i.BNO080_Init) refers to bno08x_hal.o(.data) for hi2c_bno080
    bno08x_hal.o(i.QuaternionToEulerAngles) refers to sqrtf.o(i.__hardfp_sqrtf) for __hardfp_sqrtf
    bno08x_hal.o(i.QuaternionToEulerAngles) refers to dflt_clz.o(x$fpl$dflt) for __aeabi_i2d
    bno08x_hal.o(i.QuaternionToEulerAngles) refers to dmul.o(x$fpl$dmul) for __aeabi_dmul
    bno08x_hal.o(i.QuaternionToEulerAngles) refers to ddiv.o(x$fpl$ddiv) for __aeabi_ddiv
    bno08x_hal.o(i.QuaternionToEulerAngles) refers to d2f.o(x$fpl$d2f) for __aeabi_d2f
    bno08x_hal.o(i.QuaternionToEulerAngles) refers to asinf.o(i.__hardfp_asinf) for __hardfp_asinf
    bno08x_hal.o(i.QuaternionToEulerAngles) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    bno08x_hal.o(i.QuaternionToEulerAngles) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    bno08x_hal.o(i.calibrateAccelerometer) refers to bno08x_hal.o(i.sendCalibrateCommand) for sendCalibrateCommand
    bno08x_hal.o(i.calibrateAll) refers to bno08x_hal.o(i.sendCalibrateCommand) for sendCalibrateCommand
    bno08x_hal.o(i.calibrateGyro) refers to bno08x_hal.o(i.sendCalibrateCommand) for sendCalibrateCommand
    bno08x_hal.o(i.calibrateMagnetometer) refers to bno08x_hal.o(i.sendCalibrateCommand) for sendCalibrateCommand
    bno08x_hal.o(i.calibratePlanarAccelerometer) refers to bno08x_hal.o(i.sendCalibrateCommand) for sendCalibrateCommand
    bno08x_hal.o(i.dataAvailable) refers to bno08x_hal.o(i.receivePacket) for receivePacket
    bno08x_hal.o(i.dataAvailable) refers to bno08x_hal.o(i.parseInputReport) for parseInputReport
    bno08x_hal.o(i.dataAvailable) refers to bno08x_hal.o(.data) for shtpHeader
    bno08x_hal.o(i.dataAvailable) refers to bno08x_hal.o(.bss) for shtpData
    bno08x_hal.o(i.enableAccelerometer) refers to bno08x_hal.o(i.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(i.enableGameRotationVector) refers to bno08x_hal.o(i.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(i.enableGyro) refers to bno08x_hal.o(i.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(i.enableLinearAccelerometer) refers to bno08x_hal.o(i.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(i.enableMagnetometer) refers to bno08x_hal.o(i.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(i.enableRotationVector) refers to bno08x_hal.o(i.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(i.enableStabilityClassifier) refers to bno08x_hal.o(i.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(i.enableStepCounter) refers to bno08x_hal.o(i.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(i.endCalibration) refers to bno08x_hal.o(i.sendCalibrateCommand) for sendCalibrateCommand
    bno08x_hal.o(i.frsReadRequest) refers to bno08x_hal.o(i.sendPacket) for sendPacket
    bno08x_hal.o(i.frsReadRequest) refers to bno08x_hal.o(.bss) for shtpData
    bno08x_hal.o(i.getAccelAccuracy) refers to bno08x_hal.o(.data) for accelAccuracy
    bno08x_hal.o(i.getAccelX) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getAccelX) refers to bno08x_hal.o(.data) for accelerometer_Q1
    bno08x_hal.o(i.getAccelY) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getAccelY) refers to bno08x_hal.o(.data) for accelerometer_Q1
    bno08x_hal.o(i.getAccelZ) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getAccelZ) refers to bno08x_hal.o(.data) for accelerometer_Q1
    bno08x_hal.o(i.getActivityClassifier) refers to bno08x_hal.o(.data) for activityClassifier
    bno08x_hal.o(i.getGyroAccuracy) refers to bno08x_hal.o(.data) for gyroAccuracy
    bno08x_hal.o(i.getGyroX) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getGyroX) refers to bno08x_hal.o(.data) for gyro_Q1
    bno08x_hal.o(i.getGyroY) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getGyroY) refers to bno08x_hal.o(.data) for gyro_Q1
    bno08x_hal.o(i.getGyroZ) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getGyroZ) refers to bno08x_hal.o(.data) for gyro_Q1
    bno08x_hal.o(i.getLinAccelAccuracy) refers to bno08x_hal.o(.data) for accelLinAccuracy
    bno08x_hal.o(i.getLinAccelX) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getLinAccelX) refers to bno08x_hal.o(.data) for linear_accelerometer_Q1
    bno08x_hal.o(i.getLinAccelY) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getLinAccelY) refers to bno08x_hal.o(.data) for linear_accelerometer_Q1
    bno08x_hal.o(i.getLinAccelZ) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getLinAccelZ) refers to bno08x_hal.o(.data) for linear_accelerometer_Q1
    bno08x_hal.o(i.getMagAccuracy) refers to bno08x_hal.o(.data) for magAccuracy
    bno08x_hal.o(i.getMagX) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getMagX) refers to bno08x_hal.o(.data) for magnetometer_Q1
    bno08x_hal.o(i.getMagY) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getMagY) refers to bno08x_hal.o(.data) for magnetometer_Q1
    bno08x_hal.o(i.getMagZ) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getMagZ) refers to bno08x_hal.o(.data) for magnetometer_Q1
    bno08x_hal.o(i.getQ1) refers to bno08x_hal.o(i.readFRSdata) for readFRSdata
    bno08x_hal.o(i.getQ1) refers to bno08x_hal.o(.bss) for metaData
    bno08x_hal.o(i.getQ2) refers to bno08x_hal.o(i.readFRSdata) for readFRSdata
    bno08x_hal.o(i.getQ2) refers to bno08x_hal.o(.bss) for metaData
    bno08x_hal.o(i.getQ3) refers to bno08x_hal.o(i.readFRSdata) for readFRSdata
    bno08x_hal.o(i.getQ3) refers to bno08x_hal.o(.bss) for metaData
    bno08x_hal.o(i.getQuatAccuracy) refers to bno08x_hal.o(.data) for quatAccuracy
    bno08x_hal.o(i.getQuatI) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getQuatI) refers to bno08x_hal.o(.data) for rotationVector_Q1
    bno08x_hal.o(i.getQuatJ) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getQuatJ) refers to bno08x_hal.o(.data) for rotationVector_Q1
    bno08x_hal.o(i.getQuatK) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getQuatK) refers to bno08x_hal.o(.data) for rotationVector_Q1
    bno08x_hal.o(i.getQuatRadianAccuracy) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getQuatRadianAccuracy) refers to bno08x_hal.o(.data) for rotationVector_Q1
    bno08x_hal.o(i.getQuatReal) refers to bno08x_hal.o(i.qToFloat) for qToFloat
    bno08x_hal.o(i.getQuatReal) refers to bno08x_hal.o(.data) for rotationVector_Q1
    bno08x_hal.o(i.getRange) refers to bno08x_hal.o(i.readFRSdata) for readFRSdata
    bno08x_hal.o(i.getRange) refers to bno08x_hal.o(.bss) for metaData
    bno08x_hal.o(i.getResolution) refers to bno08x_hal.o(i.readFRSdata) for readFRSdata
    bno08x_hal.o(i.getResolution) refers to bno08x_hal.o(.bss) for metaData
    bno08x_hal.o(i.getStabilityClassifier) refers to bno08x_hal.o(.data) for stabilityClassifier
    bno08x_hal.o(i.getStepCount) refers to bno08x_hal.o(.data) for stepCount
    bno08x_hal.o(i.parseInputReport) refers to bno08x_hal.o(.data) for shtpHeader
    bno08x_hal.o(i.parseInputReport) refers to bno08x_hal.o(.bss) for shtpData
    bno08x_hal.o(i.qToFloat) refers to powf.o(i.__hardfp_powf) for __hardfp_powf
    bno08x_hal.o(i.readFRSdata) refers to bno08x_hal.o(i.frsReadRequest) for frsReadRequest
    bno08x_hal.o(i.readFRSdata) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    bno08x_hal.o(i.readFRSdata) refers to bno08x_hal.o(i.receivePacket) for receivePacket
    bno08x_hal.o(i.readFRSdata) refers to bno08x_hal.o(.data) for shtpHeader
    bno08x_hal.o(i.readFRSdata) refers to bno08x_hal.o(.bss) for shtpData
    bno08x_hal.o(i.readFRSword) refers to bno08x_hal.o(i.readFRSdata) for readFRSdata
    bno08x_hal.o(i.readFRSword) refers to bno08x_hal.o(.bss) for metaData
    bno08x_hal.o(i.receivePacket) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive) for HAL_I2C_Master_Receive
    bno08x_hal.o(i.receivePacket) refers to bno08x_hal.o(.data) for _deviceAddress
    bno08x_hal.o(i.receivePacket) refers to bno08x_hal.o(.bss) for shtpData
    bno08x_hal.o(i.resetReason) refers to bno08x_hal.o(i.sendPacket) for sendPacket
    bno08x_hal.o(i.resetReason) refers to bno08x_hal.o(i.receivePacket) for receivePacket
    bno08x_hal.o(i.resetReason) refers to bno08x_hal.o(.bss) for shtpData
    bno08x_hal.o(i.saveCalibration) refers to bno08x_hal.o(i.sendCommand) for sendCommand
    bno08x_hal.o(i.saveCalibration) refers to bno08x_hal.o(.bss) for shtpData
    bno08x_hal.o(i.sendCalibrateCommand) refers to bno08x_hal.o(i.sendCommand) for sendCommand
    bno08x_hal.o(i.sendCalibrateCommand) refers to bno08x_hal.o(.bss) for shtpData
    bno08x_hal.o(i.sendCommand) refers to bno08x_hal.o(i.sendPacket) for sendPacket
    bno08x_hal.o(i.sendCommand) refers to bno08x_hal.o(.bss) for shtpData
    bno08x_hal.o(i.sendCommand) refers to bno08x_hal.o(.data) for commandSequenceNumber
    bno08x_hal.o(i.sendPacket) refers to h1_alloc.o(.text) for malloc
    bno08x_hal.o(i.sendPacket) refers to stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit) for HAL_I2C_Master_Transmit
    bno08x_hal.o(i.sendPacket) refers to h1_free.o(.text) for free
    bno08x_hal.o(i.sendPacket) refers to bno08x_hal.o(.data) for sequenceNumber
    bno08x_hal.o(i.sendPacket) refers to bno08x_hal.o(.bss) for shtpData
    bno08x_hal.o(i.setFeatureCommand) refers to bno08x_hal.o(i.sendPacket) for sendPacket
    bno08x_hal.o(i.setFeatureCommand) refers to bno08x_hal.o(.bss) for shtpData
    bno08x_hal.o(i.softReset) refers to bno08x_hal.o(i.sendPacket) for sendPacket
    bno08x_hal.o(i.softReset) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    bno08x_hal.o(i.softReset) refers to bno08x_hal.o(i.receivePacket) for receivePacket
    bno08x_hal.o(i.softReset) refers to bno08x_hal.o(.bss) for shtpData
    bno08x_hal.o(.data) refers to bno08x_hal.o(.bss) for activityConfidences
    led_driver.o(i.led_disp) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin) for HAL_GPIO_WritePin
    led_driver.o(i.led_disp) refers to led_driver.o(.data) for temp_old
    key_driver.o(i.key_read) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin) for HAL_GPIO_ReadPin
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) for HAL_UART_DMAStop
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to ringbuffer.o(i.rt_ringbuffer_put) for rt_ringbuffer_put
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to usart_app.o(.bss) for uart_rx_dma_buffer
    usart_app.o(i.HAL_UARTEx_RxEventCallback) refers to usart.o(.bss) for huart1
    usart_app.o(i.my_printf) refers to vsnprintf.o(.text) for vsnprintf
    usart_app.o(i.my_printf) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) for HAL_UART_Transmit
    usart_app.o(i.uart_init) refers to ringbuffer.o(i.rt_ringbuffer_init) for rt_ringbuffer_init
    usart_app.o(i.uart_init) refers to stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA) for HAL_UARTEx_ReceiveToIdle_DMA
    usart_app.o(i.uart_init) refers to usart_app.o(.bss) for ringbuffer_pool
    usart_app.o(i.uart_init) refers to usart.o(.bss) for huart1
    usart_app.o(i.uart_task) refers to _scanf_int.o(.text) for _scanf_int
    usart_app.o(i.uart_task) refers to ringbuffer.o(i.rt_ringbuffer_data_len) for rt_ringbuffer_data_len
    usart_app.o(i.uart_task) refers to ringbuffer.o(i.rt_ringbuffer_get) for rt_ringbuffer_get
    usart_app.o(i.uart_task) refers to __0sscanf.o(.text) for __0sscanf
    usart_app.o(i.uart_task) refers to pid.o(i.pid_set_target) for pid_set_target
    usart_app.o(i.uart_task) refers to usart_app.o(i.my_printf) for my_printf
    usart_app.o(i.uart_task) refers to motor_app.o(i.motor_set_r) for motor_set_r
    usart_app.o(i.uart_task) refers to rt_memclr_w.o(.text) for __aeabi_memclr4
    usart_app.o(i.uart_task) refers to usart_app.o(.bss) for uart_ringbuffer
    usart_app.o(i.uart_task) refers to pid_app.o(.bss) for pid_speed_left
    usart_app.o(i.uart_task) refers to usart.o(.bss) for huart1
    led_app.o(i.led_task) refers to led_driver.o(i.led_disp) for led_disp
    led_app.o(i.led_task) refers to led_app.o(.data) for led_rgb
    key_app.o(i.key_task) refers to key_driver.o(i.key_read) for key_read
    key_app.o(i.key_task) refers to key_app.o(.data) for key_old
    key_app.o(i.key_task) refers to pid_app.o(.data) for pid_running
    key_app.o(i.key_task) refers to led_app.o(.data) for led_rgb
    key_app.o(i.key_task) refers to my_timer.o(.data) for system_mode
    oled_app.o(i.my_oled_init) refers to oled.o(i.OLED_Init) for OLED_Init
    oled_app.o(i.oled_printf) refers to vsnprintf.o(.text) for vsnprintf
    oled_app.o(i.oled_printf) refers to oled.o(i.OLED_ShowStr) for OLED_ShowStr
    oled_app.o(i.oled_task) refers to bno08x_app.o(i.get_yaw) for get_yaw
    oled_app.o(i.oled_task) refers to oled_app.o(i.oled_printf) for oled_printf
    oled_app.o(i.oled_task) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    oled_app.o(i.oled_task) refers to my_timer.o(.data) for system_mode
    oled_app.o(i.oled_task) refers to gray_app.o(.data) for Digtal
    motor_app.o(i.Motor_Init) refers to motor_driver.o(i.Motor_Create) for Motor_Create
    motor_app.o(i.Motor_Init) refers to tim.o(.bss) for htim1
    motor_app.o(i.Motor_Init) refers to motor_app.o(.bss) for left_motor
    motor_app.o(i.motor_break) refers to motor_driver.o(i.Motor_Stop) for Motor_Stop
    motor_app.o(i.motor_break) refers to motor_app.o(.bss) for right_motor
    motor_app.o(i.motor_set_l) refers to motor_driver.o(i.Motor_SetSpeed) for Motor_SetSpeed
    motor_app.o(i.motor_set_l) refers to motor_app.o(.bss) for left_motor
    motor_app.o(i.motor_set_r) refers to motor_driver.o(i.Motor_SetSpeed) for Motor_SetSpeed
    motor_app.o(i.motor_set_r) refers to motor_app.o(.bss) for right_motor
    encoder_app.o(i.Encoder_Init) refers to encoder_driver.o(i.Encoder_Driver_Init) for Encoder_Driver_Init
    encoder_app.o(i.Encoder_Init) refers to tim.o(.bss) for htim3
    encoder_app.o(i.Encoder_Init) refers to encoder_app.o(.bss) for left_encoder
    encoder_app.o(i.Encoder_Task) refers to encoder_driver.o(i.Encoder_Driver_Update) for Encoder_Driver_Update
    encoder_app.o(i.Encoder_Task) refers to encoder_app.o(.bss) for left_encoder
    pid_app.o(i.Angle_PID_control) refers to bno08x_app.o(i.get_yaw) for get_yaw
    pid_app.o(i.Angle_PID_control) refers to pid.o(i.pid_calculate_positional) for pid_calculate_positional
    pid_app.o(i.Angle_PID_control) refers to pid.o(i.pid_constrain) for pid_constrain
    pid_app.o(i.Angle_PID_control) refers to pid.o(i.pid_set_target) for pid_set_target
    pid_app.o(i.Angle_PID_control) refers to pid_app.o(.bss) for pid_angle
    pid_app.o(i.Angle_PID_control) refers to pid_app.o(.data) for pid_params_angle
    pid_app.o(i.Line_PID_control) refers to pid.o(i.pid_calculate_positional) for pid_calculate_positional
    pid_app.o(i.Line_PID_control) refers to pid.o(i.pid_constrain) for pid_constrain
    pid_app.o(i.Line_PID_control) refers to pid.o(i.pid_set_target) for pid_set_target
    pid_app.o(i.Line_PID_control) refers to gray_app.o(.data) for g_line_position_error
    pid_app.o(i.Line_PID_control) refers to pid_app.o(.bss) for pid_line
    pid_app.o(i.Line_PID_control) refers to pid_app.o(.data) for pid_params_line
    pid_app.o(i.PID_Init) refers to pid.o(i.pid_init) for pid_init
    pid_app.o(i.PID_Init) refers to pid.o(i.pid_set_target) for pid_set_target
    pid_app.o(i.PID_Init) refers to pid_app.o(.data) for pid_params_left
    pid_app.o(i.PID_Init) refers to pid_app.o(.bss) for pid_speed_left
    pid_app.o(i.PID_Task) refers to pid.o(i.pid_set_target) for pid_set_target
    pid_app.o(i.PID_Task) refers to pid_app.o(i.Angle_PID_control) for Angle_PID_control
    pid_app.o(i.PID_Task) refers to pid_app.o(i.Line_PID_control) for Line_PID_control
    pid_app.o(i.PID_Task) refers to pid.o(i.pid_calculate_positional) for pid_calculate_positional
    pid_app.o(i.PID_Task) refers to pid.o(i.pid_constrain) for pid_constrain
    pid_app.o(i.PID_Task) refers to motor_app.o(i.motor_set_l) for motor_set_l
    pid_app.o(i.PID_Task) refers to motor_app.o(i.motor_set_r) for motor_set_r
    pid_app.o(i.PID_Task) refers to pid_app.o(.data) for pid_running
    pid_app.o(i.PID_Task) refers to my_timer.o(.data) for system_mode
    pid_app.o(i.PID_Task) refers to pid_app.o(.bss) for pid_angle
    pid_app.o(i.PID_Task) refers to encoder_app.o(.bss) for left_encoder
    gray_app.o(i.Gray_Init) refers to software_iic.o(i.Ping) for Ping
    gray_app.o(i.Gray_Init) refers to usart_app.o(i.my_printf) for my_printf
    gray_app.o(i.Gray_Init) refers to usart.o(.bss) for huart1
    gray_app.o(i.Gray_Task) refers to software_iic.o(i.IIC_Get_Digtal) for IIC_Get_Digtal
    gray_app.o(i.Gray_Task) refers to gray_app.o(.data) for Digtal
    bno08x_app.o(i.bno080_task) refers to bno08x_hal.o(i.dataAvailable) for dataAvailable
    bno08x_app.o(i.bno080_task) refers to bno08x_hal.o(i.getQuatI) for getQuatI
    bno08x_app.o(i.bno080_task) refers to bno08x_hal.o(i.getQuatJ) for getQuatJ
    bno08x_app.o(i.bno080_task) refers to bno08x_hal.o(i.getQuatK) for getQuatK
    bno08x_app.o(i.bno080_task) refers to bno08x_hal.o(i.getQuatReal) for getQuatReal
    bno08x_app.o(i.bno080_task) refers to bno08x_hal.o(i.QuaternionToEulerAngles) for QuaternionToEulerAngles
    bno08x_app.o(i.bno080_task) refers to bno08x_app.o(.data) for yaw
    bno08x_app.o(i.convert_to_continuous_yaw) refers to bno08x_app.o(.data) for g_is_yaw_initialized
    bno08x_app.o(i.get_pitch) refers to bno08x_app.o(.data) for pitch
    bno08x_app.o(i.get_roll) refers to bno08x_app.o(.data) for roll
    bno08x_app.o(i.get_yaw) refers to bno08x_app.o(i.convert_to_continuous_yaw) for convert_to_continuous_yaw
    bno08x_app.o(i.get_yaw) refers to bno08x_app.o(.data) for yaw
    bno08x_app.o(i.my_bno080_init) refers to usart_app.o(i.my_printf) for my_printf
    bno08x_app.o(i.my_bno080_init) refers to bno08x_hal.o(i.BNO080_Init) for BNO080_Init
    bno08x_app.o(i.my_bno080_init) refers to bno08x_hal.o(i.BNO080_HardwareReset) for BNO080_HardwareReset
    bno08x_app.o(i.my_bno080_init) refers to bno08x_hal.o(i.softReset) for softReset
    bno08x_app.o(i.my_bno080_init) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    bno08x_app.o(i.my_bno080_init) refers to bno08x_hal.o(i.enableRotationVector) for enableRotationVector
    bno08x_app.o(i.my_bno080_init) refers to usart.o(.bss) for huart1
    bno08x_app.o(i.my_bno080_init) refers to i2c.o(.bss) for hi2c1
    my_scheduler.o(i.all_task_init) refers to oled_app.o(i.my_oled_init) for my_oled_init
    my_scheduler.o(i.all_task_init) refers to usart_app.o(i.uart_init) for uart_init
    my_scheduler.o(i.all_task_init) refers to bno08x_app.o(i.my_bno080_init) for my_bno080_init
    my_scheduler.o(i.all_task_init) refers to led_app.o(i.led_init) for led_init
    my_scheduler.o(i.all_task_init) refers to pid_app.o(i.PID_Init) for PID_Init
    my_scheduler.o(i.all_task_init) refers to encoder_app.o(i.Encoder_Init) for Encoder_Init
    my_scheduler.o(i.all_task_init) refers to motor_app.o(i.Motor_Init) for Motor_Init
    my_scheduler.o(i.all_task_init) refers to gray_app.o(i.Gray_Init) for Gray_Init
    my_scheduler.o(i.all_task_init) refers to my_timer.o(i.timer_init) for timer_init
    my_scheduler.o(i.all_task_init) refers to my_scheduler.o(.data) for task_num
    my_scheduler.o(i.all_task_run) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    my_scheduler.o(i.all_task_run) refers to my_scheduler.o(.data) for all_task
    my_scheduler.o(.data) refers to led_app.o(i.led_task) for led_task
    my_scheduler.o(.data) refers to usart_app.o(i.uart_task) for uart_task
    my_scheduler.o(.data) refers to key_app.o(i.key_task) for key_task
    my_scheduler.o(.data) refers to oled_app.o(i.oled_task) for oled_task
    my_timer.o(i.Car_State_Update) refers to pid.o(i.pid_set_target) for pid_set_target
    my_timer.o(i.Car_State_Update) refers to pid.o(i.pid_reset) for pid_reset
    my_timer.o(i.Car_State_Update) refers to led_app.o(.data) for led_rgb
    my_timer.o(i.Car_State_Update) refers to my_timer.o(.data) for distance
    my_timer.o(i.Car_State_Update) refers to pid_app.o(.data) for stop_flat
    my_timer.o(i.Car_State_Update) refers to pid_app.o(.bss) for pid_angle
    my_timer.o(i.HAL_TIM_PeriodElapsedCallback) refers to encoder_app.o(i.Encoder_Task) for Encoder_Task
    my_timer.o(i.HAL_TIM_PeriodElapsedCallback) refers to gray_app.o(i.Gray_Task) for Gray_Task
    my_timer.o(i.HAL_TIM_PeriodElapsedCallback) refers to bno08x_app.o(i.bno080_task) for bno080_task
    my_timer.o(i.HAL_TIM_PeriodElapsedCallback) refers to pid_app.o(i.PID_Task) for PID_Task
    my_timer.o(i.HAL_TIM_PeriodElapsedCallback) refers to my_timer.o(i.Car_State_Update) for Car_State_Update
    my_timer.o(i.HAL_TIM_PeriodElapsedCallback) refers to tim.o(.bss) for htim2
    my_timer.o(i.HAL_TIM_PeriodElapsedCallback) refers to my_timer.o(.data) for measure_timer5ms
    my_timer.o(i.HAL_TIM_PeriodElapsedCallback) refers to encoder_app.o(.bss) for left_encoder
    my_timer.o(i.HAL_TIM_PeriodElapsedCallback) refers to gray_app.o(.data) for Digtal
    my_timer.o(i.HAL_TIM_PeriodElapsedCallback) refers to led_app.o(.data) for led_rgb
    my_timer.o(i.timer_init) refers to stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT) for HAL_TIM_Base_Start_IT
    my_timer.o(i.timer_init) refers to tim.o(.bss) for htim2
    malloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    malloc.o(.text) refers (Special) to init_alloc.o(.text) for _init_alloc
    malloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    malloc.o(.text) refers to heapstubs.o(.text) for __Heap_Alloc
    free.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    free.o(.text) refers to heapstubs.o(.text) for __Heap_Free
    h1_alloc.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_alloc.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_free.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_alloc_mt.o(.text) refers (Special) to h1_init.o(.text) for __Heap_Initialize
    h1_alloc_mt.o(.text) refers to init_alloc.o(.text) for __Heap_Full
    h1_alloc_mt.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    h1_free_mt.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._FDIterate) refers to heap2.o(.conststring) for .conststring
    heap2.o(i.___Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i.___Heap_ProvideMemory$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i.___Heap_Stats$realtime) refers to heap2.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2.o(i.___Heap_Valid$realtime) refers to heap2.o(i._FDIterate) for _FDIterate
    heap2.o(i.___Heap_Valid$realtime) refers to heap2.o(.conststring) for .conststring
    heap2.o(i._free$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._free$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i._free$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._malloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i._malloc$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i._malloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._posix_memalign$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2.o(i._posix_memalign$realtime) refers to init_alloc.o(.text) for __Heap_Full
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2.o(i._posix_memalign$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._realloc$realtime) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2.o(i._realloc$realtime) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2.o(i._realloc$realtime) refers to h1_free.o(.text) for free
    heap2.o(i._realloc$realtime) refers to h1_alloc.o(.text) for malloc
    heap2.o(i._realloc$realtime) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2.o(i._realloc$realtime) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    heap2mt.o(i._FDIterate) refers to heap2mt.o(.conststring) for .conststring
    heap2mt.o(i.___Heap_Initialize$realtime$concurrent) refers to mutex_dummy.o(.text) for _mutex_initialize
    heap2mt.o(i.___Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i.___Heap_ProvideMemory$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i.___Heap_Stats$realtime$concurrent) refers to heap2mt.o(i._Heap2_StatsIterate) for _Heap2_StatsIterate
    heap2mt.o(i.___Heap_Valid$realtime$concurrent) refers to heap2mt.o(i._FDIterate) for _FDIterate
    heap2mt.o(i.___Heap_Valid$realtime$concurrent) refers to heap2mt.o(.conststring) for .conststring
    heap2mt.o(i._free$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._free$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i._free$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._malloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2mt.o(i._malloc$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2mt.o(i._malloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_FindFirst) for _FDTree_FindFirst
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to init_alloc.o(.text) for __Heap_Full
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_RemoveNode) for _FDTree_RemoveNode
    heap2mt.o(i._posix_memalign$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._realloc$realtime$concurrent) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    heap2mt.o(i._realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Insert) for _FDTree_Insert
    heap2mt.o(i._realloc$realtime$concurrent) refers to h1_free.o(.text) for free
    heap2mt.o(i._realloc$realtime$concurrent) refers to h1_alloc.o(.text) for malloc
    heap2mt.o(i._realloc$realtime$concurrent) refers to fdtree.o(i._FDTree_Delete) for _FDTree_Delete
    heap2mt.o(i._realloc$realtime$concurrent) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    vsnprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsnprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsnprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsnprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsnprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsnprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsnprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsnprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsnprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsnprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsnprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsnprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsnprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsnprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsnprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsnprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsnprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsnprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsnprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsnprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsnprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsnprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsnprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsnprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsnprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsnprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsnprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsnprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsnprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsnprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsnprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsnprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsnprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsnprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsnprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsnprintf.o(.text) refers to _sputc.o(.text) for _sputc
    vsnprintf.o(.text) refers to _snputc.o(.text) for _snputc
    __0sscanf.o(.text) refers to scanf_char.o(.text) for __vfscanf_char
    __0sscanf.o(.text) refers to _sgetc.o(.text) for _sgetc
    _scanf_int.o(.text) refers to _chval.o(.text) for _chval
    assert.o(.text) refers to assert_puts.o(.text) for __assert_puts
    assert.o(.text) refers to abort.o(.text) for abort
    rt_memcpy_v6.o(.text) refers to rt_memcpy_w.o(.text) for __aeabi_memcpy4
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    d2f.o(x$fpl$d2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    d2f.o(x$fpl$d2f) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    d2f.o(x$fpl$d2f) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    ddiv.o(x$fpl$drdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$drdiv) refers to ddiv.o(x$fpl$ddiv) for ddiv_entry
    ddiv.o(x$fpl$ddiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    ddiv.o(x$fpl$ddiv) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    ddiv.o(x$fpl$ddiv) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    dflt_clz.o(x$fpl$dfltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dflt_clz.o(x$fpl$dfltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dmul.o(x$fpl$dmul) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    dmul.o(x$fpl$dmul) refers to dnaninf.o(x$fpl$dnaninf) for __fpl_dnaninf
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    asinf.o(i.__hardfp_asinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asinf.o(i.__hardfp_asinf) refers to sqrtf.o(i.sqrtf) for sqrtf
    asinf.o(i.__hardfp_asinf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    asinf.o(i.__hardfp_asinf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    asinf.o(i.__hardfp_asinf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    asinf.o(i.__hardfp_asinf) refers to _rserrno.o(.text) for __set_errno
    asinf.o(i.__hardfp_asinf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    asinf.o(i.__softfp_asinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asinf.o(i.__softfp_asinf) refers to asinf.o(i.__hardfp_asinf) for __hardfp_asinf
    asinf.o(i.asinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asinf.o(i.asinf) refers to asinf.o(i.__hardfp_asinf) for __hardfp_asinf
    asinf_x.o(i.____hardfp_asinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asinf_x.o(i.____hardfp_asinf$lsc) refers to sqrtf.o(i.sqrtf) for sqrtf
    asinf_x.o(i.____hardfp_asinf$lsc) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    asinf_x.o(i.____hardfp_asinf$lsc) refers to _rserrno.o(.text) for __set_errno
    asinf_x.o(i.____softfp_asinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asinf_x.o(i.____softfp_asinf$lsc) refers to asinf_x.o(i.____hardfp_asinf$lsc) for ____hardfp_asinf$lsc
    asinf_x.o(i.__asinf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    asinf_x.o(i.__asinf$lsc) refers to asinf_x.o(i.____hardfp_asinf$lsc) for ____hardfp_asinf$lsc
    atan2f.o(i.__hardfp_atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.__hardfp_atan2f) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    atan2f.o(i.__hardfp_atan2f) refers to _rserrno.o(.text) for __set_errno
    atan2f.o(i.__hardfp_atan2f) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f.o(i.__softfp_atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.__softfp_atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    atan2f.o(i.atan2f) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f.o(i.atan2f) refers to atan2f.o(i.__hardfp_atan2f) for __hardfp_atan2f
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to _rserrno.o(.text) for __set_errno
    atan2f_x.o(i.____hardfp_atan2f$lsc) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    atan2f_x.o(i.____softfp_atan2f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f_x.o(i.____softfp_atan2f$lsc) refers to atan2f_x.o(i.____hardfp_atan2f$lsc) for ____hardfp_atan2f$lsc
    atan2f_x.o(i.__atan2f$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    atan2f_x.o(i.__atan2f$lsc) refers to atan2f_x.o(i.____hardfp_atan2f$lsc) for ____hardfp_atan2f$lsc
    powf.o(i.__hardfp_powf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    powf.o(i.__hardfp_powf) refers to _rserrno.o(.text) for __set_errno
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_overflow) for __mathlib_flt_overflow
    powf.o(i.__hardfp_powf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    powf.o(i.__hardfp_powf) refers to powf.o(.constdata) for .constdata
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    powf.o(i.__hardfp_powf) refers to funder.o(i.__mathlib_flt_divzero) for __mathlib_flt_divzero
    powf.o(i.__softfp_powf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    powf.o(i.__softfp_powf) refers to powf.o(i.__hardfp_powf) for __hardfp_powf
    powf.o(i.powf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    powf.o(i.powf) refers to powf.o(i.__hardfp_powf) for __hardfp_powf
    powf.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    powf_x.o(i.____hardfp_powf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    powf_x.o(i.____hardfp_powf$lsc) refers to _rserrno.o(.text) for __set_errno
    powf_x.o(i.____hardfp_powf$lsc) refers to powf_x.o(.constdata) for .constdata
    powf_x.o(i.____hardfp_powf$lsc) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    powf_x.o(i.____softfp_powf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    powf_x.o(i.____softfp_powf$lsc) refers to powf_x.o(i.____hardfp_powf$lsc) for ____hardfp_powf$lsc
    powf_x.o(i.__powf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    powf_x.o(i.__powf$lsc) refers to powf_x.o(i.____hardfp_powf$lsc) for ____hardfp_powf$lsc
    powf_x.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.__hardfp_sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.__hardfp_sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf.o(i.__softfp_sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf.o(i.sqrtf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf.o(i.sqrtf) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.____hardfp_sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.____softfp_sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    sqrtf_x.o(i.__sqrtf$lsc) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sqrtf_x.o(i.__sqrtf$lsc) refers to _rserrno.o(.text) for __set_errno
    roundf.o(i.__hardfp_roundf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    roundf.o(i.__hardfp_roundf) refers to frnd.o(x$fpl$frnd) for _frnd
    roundf.o(i.roundf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    roundf.o(i.roundf) refers to roundf.o(i.__hardfp_roundf) for __hardfp_roundf
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    aeabi_ldiv0_sigfpe.o(.text) refers to rt_div0.o(.text) for __rt_div0
    rt_heap_descriptor.o(.text) refers to rt_heap_descriptor.o(.bss) for __rt_heap_descriptor_data
    rt_heap_descriptor_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    init_alloc.o(.text) refers (Special) to hguard.o(.text) for __heap$guard
    init_alloc.o(.text) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000005) for __rt_lib_init_heap_2
    init_alloc.o(.text) refers (Special) to maybetermalloc1.o(.emb_text) for _maybe_terminate_alloc
    init_alloc.o(.text) refers to h1_extend.o(.text) for __Heap_ProvideMemory
    init_alloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    init_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    init_alloc.o(.text) refers to h1_init.o(.text) for __Heap_Initialize
    h1_init_mt.o(.text) refers to mutex_dummy.o(.text) for _mutex_initialize
    _rserrno.o(.text) refers to rt_errno_addr_intlibspace.o(.text) for __aeabi_errno_addr
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    scanf_char.o(.text) refers to _scanf.o(.text) for __vfscanf
    scanf_char.o(.text) refers to isspace.o(.text) for isspace
    abort.o(.text) refers to defsig_abrt_outer.o(.text) for __rt_SIGABRT
    abort.o(.text) refers (Weak) to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    abort.o(.text) refers to sys_exit.o(.text) for _sys_exit
    assert_puts.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    _get_argv.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv.o(.text) refers to h1_alloc.o(.text) for malloc
    _get_argv.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libinit2.o(.ARM.Collect$$libinit$$00000001) refers to fpinit.o(x$fpl$fpinit) for _fp_init
    libinit2.o(.ARM.Collect$$libinit$$00000005) refers (Weak) to init_alloc.o(.text) for _init_alloc
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    dnaninf.o(x$fpl$dnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frnd.o(x$fpl$frnd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    frnd.o(x$fpl$frnd) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    sys_exit.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_exit.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(i.main) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_div0.o(.text) refers to defsig_fpe_outer.o(.text) for __rt_SIGFPE
    rt_errno_addr.o(.text) refers to rt_errno_addr.o(.bss) for __aeabi_errno_addr_data
    rt_errno_addr_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    maybetermalloc2.o(.emb_text) refers (Special) to term_alloc.o(.text) for _terminate_alloc
    h1_extend.o(.text) refers to h1_free.o(.text) for free
    h1_extend_mt.o(.text) refers to h1_free_mt.o(.text) for _free_internal
    isspace.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    _scanf.o(.text) refers (Weak) to _scanf_int.o(.text) for _scanf_int
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    defsig_abrt_outer.o(.text) refers to defsig_abrt_inner.o(.text) for __rt_SIGABRT_inner
    defsig_abrt_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_abrt_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    heapauxa.o(.text) refers to heapauxa.o(.data) for .data
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f407xx.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to sys_exit.o(.text) for _sys_exit
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    term_alloc.o(.text) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000010) for __rt_lib_shutdown_heap_2
    term_alloc.o(.text) refers to rt_heap_descriptor_intlibspace.o(.text) for __rt_heap_descriptor
    term_alloc.o(.text) refers to h1_final.o(.text) for __Heap_Finalize
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    defsig_exit.o(.text) refers to sys_exit.o(.text) for _sys_exit
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_outer.o(.text) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig_fpe_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_fpe_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000001) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F) refers (Weak) to term_alloc.o(.text) for _terminate_alloc
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to sys_exit.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    defsig.o(CL$$defsig) refers to defsig_abrt_inner.o(.text) for __rt_SIGABRT_inner
    defsig.o(CL$$defsig) refers to defsig_fpe_inner.o(.text) for __rt_SIGFPE_inner
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing i2c.o(.rev16_text), (4 bytes).
    Removing i2c.o(.revsh_text), (4 bytes).
    Removing i2c.o(.rrx_text), (6 bytes).
    Removing i2c.o(i.HAL_I2C_MspDeInit), (100 bytes).
    Removing tim.o(.rev16_text), (4 bytes).
    Removing tim.o(.revsh_text), (4 bytes).
    Removing tim.o(.rrx_text), (6 bytes).
    Removing tim.o(i.HAL_TIM_Base_MspDeInit), (68 bytes).
    Removing tim.o(i.HAL_TIM_Encoder_MspDeInit), (88 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (128 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_AddrCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DeInit), (60 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_DisableListen_IT), (70 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ER_IRQHandler), (212 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_EV_IRQHandler), (474 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_EnableListen_IT), (70 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetError), (6 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetMode), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_GetState), (8 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_IsDeviceReady), (440 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_ListenCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MasterTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Abort_IT), (126 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive_IT), (244 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_DMA), (676 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Receive_IT), (364 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_DMA), (548 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Seq_Transmit_IT), (288 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit_IT), (232 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MemRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MemTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read), (776 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_DMA), (612 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Read_IT), (264 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_DMA), (540 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write_IT), (244 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_MspInit), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveRxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_SlaveTxCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive), (384 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_DMA), (308 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Receive_IT), (152 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Receive_IT), (158 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_DMA), (460 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Seq_Transmit_IT), (158 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit), (404 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_DMA), (308 bytes).
    Removing stm32f4xx_hal_i2c.o(i.HAL_I2C_Slave_Transmit_IT), (152 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_ConvertOtherXferOptions), (28 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAAbort), (248 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAError), (80 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_DMAXferCplt), (318 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Flush_DR), (18 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_ITError), (428 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_BTF), (304 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterReceive_RXNE), (260 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_BTF), (160 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MasterTransmit_TXE), (216 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_ADD10), (42 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_ADDR), (616 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Master_SB), (158 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_MemoryTransmit_TXE_BTF), (198 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryRead), (352 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_SlaveReceive_BTF), (26 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_SlaveReceive_RXNE), (70 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_SlaveTransmit_BTF), (26 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_SlaveTransmit_TXE), (70 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_ADDR), (112 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_AF), (168 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_Slave_STOPF), (396 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPFlagUntilTimeout), (98 bytes).
    Removing stm32f4xx_hal_i2c.o(i.I2C_WaitOnSTOPRequestThroughIT), (80 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_i2c_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (68 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (244 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (192 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (32 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (56 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (112 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (56 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (148 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (416 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (384 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Byte), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (48 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord), (40 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Word), (40 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode), (152 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (100 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (260 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (28 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (20 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program), (144 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (112 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock), (52 bytes).
    Removing stm32f4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (92 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (120 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase), (48 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BOR_LevelConfig), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (40 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (40 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetBOR), (16 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (36 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (56 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (172 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (100 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (34 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (148 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (360 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (46 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (20 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (32 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (18 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (124 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (4056 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (112 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (8 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (346 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (96 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (102 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (124 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (180 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (28 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (28 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (20 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (20 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (28 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (20 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (20 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (16 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (40 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (68 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (32 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (120 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_CORTEX_ClearEvent), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (92 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (32 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_DisableRegion), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (40 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_EnableRegion), (28 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (44 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (48 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (48 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (148 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (44 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (40 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (20 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (80 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (8 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetREVID), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (18 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (48 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (18 bytes).
    Removing stm32f4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (120 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (28 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (156 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (14 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetPending), (36 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (44 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (240 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_DeInit), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start), (156 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_DMA), (248 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop), (50 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_DMA), (70 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Stop_IT), (62 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigOCrefClear), (278 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigTI1Input), (22 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurstState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiReadStart), (476 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_MultiWriteStart), (476 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStart), (42 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_ReadStop), (140 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStart), (42 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_DMABurst_WriteStop), (140 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_DMA), (556 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start_IT), (252 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop), (214 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_DMA), (280 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Stop_IT), (256 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GenerateEvent), (54 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetActiveChannel), (6 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_GetChannelState), (38 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_ConfigChannel), (222 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_DeInit), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Init), (102 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start), (316 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_DMA), (608 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Start_IT), (400 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop), (138 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_DMA), (244 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_IC_Stop_IT), (222 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_ConfigChannel), (112 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DeInit), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Init), (102 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start), (268 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_DMA), (588 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Start_IT), (352 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop), (168 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_DMA), (276 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OC_Stop_IT), (252 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_ConfigChannel), (310 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Init), (102 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start), (140 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Start_IT), (164 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop), (160 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_OnePulse_Stop_IT), (184 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_DeInit), (120 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_DMA), (588 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start_IT), (352 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop), (168 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_DMA), (276 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Stop_IT), (252 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_PeriodElapsedHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_ReadCapturedValue), (50 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro), (108 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_SlaveConfigSynchro_IT), (108 bytes).
    Removing stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureCplt), (122 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMACaptureHalfCplt), (64 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseCplt), (104 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMADelayPulseHalfCplt), (64 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAError), (94 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedCplt), (26 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMAPeriodElapsedHalfCplt), (14 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerCplt), (26 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_DMATriggerHalfCplt), (14 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_SlaveTimer_SetConfig), (178 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI1_SetConfig), (144 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI2_SetConfig), (58 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI3_SetConfig), (56 bytes).
    Removing stm32f4xx_hal_tim.o(i.TIM_TI4_SetConfig), (60 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent), (134 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_DMA), (164 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigCommutEvent_IT), (134 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_GetChannelNState), (38 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_DeInit), (96 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_GetState), (8 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Init), (234 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_MspInit), (2 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start), (216 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_DMA), (280 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Start_IT), (228 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop), (78 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_DMA), (84 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_HallSensor_Stop_IT), (90 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start), (244 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_DMA), (504 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Start_IT), (324 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop), (136 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_DMA), (220 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OCN_Stop_IT), (230 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start), (118 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Start_IT), (142 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop), (138 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_OnePulseN_Stop_IT), (162 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start), (244 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_DMA), (504 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Start_IT), (324 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop), (136 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_DMA), (220 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_PWMN_Stop_IT), (230 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_RemapConfig), (42 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationCplt), (20 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIMEx_DMACommutationHalfCplt), (20 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_CCxNChannelCmd), (34 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMADelayPulseNCplt), (82 bytes).
    Removing stm32f4xx_hal_tim_ex.o(i.TIM_DMAErrorCCxN), (68 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (68 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (68 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (122 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (146 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (80 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (82 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (82 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (158 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_GetRxEventType), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle), (296 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_IT), (114 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UARTEx_RxEventCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (282 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (196 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (220 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (126 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (144 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (336 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (166 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (170 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetState), (20 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (194 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT), (44 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA), (156 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (56 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (56 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (28 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt), (88 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (56 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt), (14 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (24 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_Start_Receive_IT), (64 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (180 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_getchar), (160 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_peek), (172 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_put_force), (304 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar), (164 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_putchar_force), (200 bytes).
    Removing ringbuffer.o(i.rt_ringbuffer_reset), (112 bytes).
    Removing software_iic.o(.rev16_text), (4 bytes).
    Removing software_iic.o(.revsh_text), (4 bytes).
    Removing software_iic.o(.rrx_text), (6 bytes).
    Removing software_iic.o(i.IIC_Anolog_Normalize), (16 bytes).
    Removing software_iic.o(i.IIC_Get_Anolog), (28 bytes).
    Removing software_iic.o(i.IIC_Get_Offset), (32 bytes).
    Removing software_iic.o(i.IIC_Get_Single_Anolog), (26 bytes).
    Removing software_iic.o(i.IIC_ReadByte), (34 bytes).
    Removing software_iic.o(i.IIC_WriteByte), (70 bytes).
    Removing software_iic.o(i.IIC_WriteBytes), (88 bytes).
    Removing pid.o(i.pid_app_limit_integral), (40 bytes).
    Removing pid.o(i.pid_calculate_incremental), (38 bytes).
    Removing pid.o(i.pid_formula_incremental), (142 bytes).
    Removing pid.o(i.pid_set_limit), (6 bytes).
    Removing pid.o(i.pid_set_params), (14 bytes).
    Removing motor_driver.o(.rev16_text), (4 bytes).
    Removing motor_driver.o(.revsh_text), (4 bytes).
    Removing motor_driver.o(.rrx_text), (6 bytes).
    Removing motor_driver.o(i.Motor_Enable), (60 bytes).
    Removing motor_driver.o(i.Motor_GetState), (22 bytes).
    Removing motor_driver.o(i.Motor_SetDecayMode), (70 bytes).
    Removing motor_driver.o(i.Motor_Stop), (52 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing oled.o(.rrx_text), (6 bytes).
    Removing oled.o(i.OLED_Allfill), (56 bytes).
    Removing oled.o(i.OLED_Display_Off), (22 bytes).
    Removing oled.o(i.OLED_Display_On), (22 bytes).
    Removing oled.o(i.OLED_Pow), (22 bytes).
    Removing oled.o(i.OLED_ShowFloat), (352 bytes).
    Removing oled.o(i.OLED_ShowHanzi), (100 bytes).
    Removing oled.o(i.OLED_ShowHzbig), (184 bytes).
    Removing oled.o(i.OLED_ShowNum), (136 bytes).
    Removing oled.o(i.OLED_ShowPic), (76 bytes).
    Removing encoder_driver.o(.rev16_text), (4 bytes).
    Removing encoder_driver.o(.revsh_text), (4 bytes).
    Removing encoder_driver.o(.rrx_text), (6 bytes).
    Removing bno08x_hal.o(.rev16_text), (4 bytes).
    Removing bno08x_hal.o(.revsh_text), (4 bytes).
    Removing bno08x_hal.o(.rrx_text), (6 bytes).
    Removing bno08x_hal.o(i.calibrateAccelerometer), (10 bytes).
    Removing bno08x_hal.o(i.calibrateAll), (10 bytes).
    Removing bno08x_hal.o(i.calibrateGyro), (10 bytes).
    Removing bno08x_hal.o(i.calibrateMagnetometer), (10 bytes).
    Removing bno08x_hal.o(i.calibratePlanarAccelerometer), (10 bytes).
    Removing bno08x_hal.o(i.enableAccelerometer), (16 bytes).
    Removing bno08x_hal.o(i.enableGameRotationVector), (16 bytes).
    Removing bno08x_hal.o(i.enableGyro), (16 bytes).
    Removing bno08x_hal.o(i.enableLinearAccelerometer), (16 bytes).
    Removing bno08x_hal.o(i.enableMagnetometer), (16 bytes).
    Removing bno08x_hal.o(i.enableStabilityClassifier), (16 bytes).
    Removing bno08x_hal.o(i.enableStepCounter), (16 bytes).
    Removing bno08x_hal.o(i.endCalibration), (10 bytes).
    Removing bno08x_hal.o(i.frsReadRequest), (52 bytes).
    Removing bno08x_hal.o(i.getAccelAccuracy), (12 bytes).
    Removing bno08x_hal.o(i.getAccelX), (28 bytes).
    Removing bno08x_hal.o(i.getAccelY), (28 bytes).
    Removing bno08x_hal.o(i.getAccelZ), (28 bytes).
    Removing bno08x_hal.o(i.getActivityClassifier), (12 bytes).
    Removing bno08x_hal.o(i.getGyroAccuracy), (12 bytes).
    Removing bno08x_hal.o(i.getGyroX), (28 bytes).
    Removing bno08x_hal.o(i.getGyroY), (28 bytes).
    Removing bno08x_hal.o(i.getGyroZ), (28 bytes).
    Removing bno08x_hal.o(i.getLinAccelAccuracy), (12 bytes).
    Removing bno08x_hal.o(i.getLinAccelX), (28 bytes).
    Removing bno08x_hal.o(i.getLinAccelY), (28 bytes).
    Removing bno08x_hal.o(i.getLinAccelZ), (28 bytes).
    Removing bno08x_hal.o(i.getMagAccuracy), (12 bytes).
    Removing bno08x_hal.o(i.getMagX), (28 bytes).
    Removing bno08x_hal.o(i.getMagY), (28 bytes).
    Removing bno08x_hal.o(i.getMagZ), (28 bytes).
    Removing bno08x_hal.o(i.getQ1), (32 bytes).
    Removing bno08x_hal.o(i.getQ2), (36 bytes).
    Removing bno08x_hal.o(i.getQ3), (32 bytes).
    Removing bno08x_hal.o(i.getQuatAccuracy), (12 bytes).
    Removing bno08x_hal.o(i.getQuatRadianAccuracy), (28 bytes).
    Removing bno08x_hal.o(i.getRange), (44 bytes).
    Removing bno08x_hal.o(i.getResolution), (44 bytes).
    Removing bno08x_hal.o(i.getStabilityClassifier), (12 bytes).
    Removing bno08x_hal.o(i.getStepCount), (12 bytes).
    Removing bno08x_hal.o(i.readFRSdata), (212 bytes).
    Removing bno08x_hal.o(i.readFRSword), (32 bytes).
    Removing bno08x_hal.o(i.resetReason), (52 bytes).
    Removing bno08x_hal.o(i.saveCalibration), (32 bytes).
    Removing bno08x_hal.o(i.sendCalibrateCommand), (100 bytes).
    Removing bno08x_hal.o(i.sendCommand), (68 bytes).
    Removing led_driver.o(.rev16_text), (4 bytes).
    Removing led_driver.o(.revsh_text), (4 bytes).
    Removing led_driver.o(.rrx_text), (6 bytes).
    Removing key_driver.o(.rev16_text), (4 bytes).
    Removing key_driver.o(.revsh_text), (4 bytes).
    Removing key_driver.o(.rrx_text), (6 bytes).
    Removing usart_app.o(.rev16_text), (4 bytes).
    Removing usart_app.o(.revsh_text), (4 bytes).
    Removing usart_app.o(.rrx_text), (6 bytes).
    Removing led_app.o(.rev16_text), (4 bytes).
    Removing led_app.o(.revsh_text), (4 bytes).
    Removing led_app.o(.rrx_text), (6 bytes).
    Removing key_app.o(.rev16_text), (4 bytes).
    Removing key_app.o(.revsh_text), (4 bytes).
    Removing key_app.o(.rrx_text), (6 bytes).
    Removing oled_app.o(.rev16_text), (4 bytes).
    Removing oled_app.o(.revsh_text), (4 bytes).
    Removing oled_app.o(.rrx_text), (6 bytes).
    Removing motor_app.o(.rev16_text), (4 bytes).
    Removing motor_app.o(.revsh_text), (4 bytes).
    Removing motor_app.o(.rrx_text), (6 bytes).
    Removing motor_app.o(i.motor_break), (24 bytes).
    Removing encoder_app.o(.rev16_text), (4 bytes).
    Removing encoder_app.o(.revsh_text), (4 bytes).
    Removing encoder_app.o(.rrx_text), (6 bytes).
    Removing pid_app.o(.rev16_text), (4 bytes).
    Removing pid_app.o(.revsh_text), (4 bytes).
    Removing pid_app.o(.rrx_text), (6 bytes).
    Removing gray_app.o(.rev16_text), (4 bytes).
    Removing gray_app.o(.revsh_text), (4 bytes).
    Removing gray_app.o(.rrx_text), (6 bytes).
    Removing bno08x_app.o(.rev16_text), (4 bytes).
    Removing bno08x_app.o(.revsh_text), (4 bytes).
    Removing bno08x_app.o(.rrx_text), (6 bytes).
    Removing bno08x_app.o(i.get_pitch), (12 bytes).
    Removing bno08x_app.o(i.get_roll), (12 bytes).
    Removing my_scheduler.o(.rev16_text), (4 bytes).
    Removing my_scheduler.o(.revsh_text), (4 bytes).
    Removing my_scheduler.o(.rrx_text), (6 bytes).
    Removing my_timer.o(.rev16_text), (4 bytes).
    Removing my_timer.o(.revsh_text), (4 bytes).
    Removing my_timer.o(.rrx_text), (6 bytes).

596 unused section(s) (total 54710 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Core/Src/dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ../Core/Src/gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ../Core/Src/i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ../Core/Src/main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ../Core/Src/stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ../Core/Src/stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ../Core/Src/system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ../Core/Src/tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ../Core/Src/usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/dczerorl2.s                0x00000000   Number         0  __dczerorl2.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_heap_descriptor.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0_sigfpe.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  aeabi_ldiv0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_errno_addr.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_div0.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_heap_descriptor_intlibspace.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  mutex_dummy.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_exit.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/assert.c                         0x00000000   Number         0  assert_puts.o ABSOLUTE
    ../clib/assert.c                         0x00000000   Number         0  assert.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/ctype.c                          0x00000000   Number         0  isspace.o ABSOLUTE
    ../clib/fenv.c                           0x00000000   Number         0  _rserrno.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_free.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_alloc_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_free_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_final_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_final.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_extend_mt.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_alloc.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_init.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_extend.o ABSOLUTE
    ../clib/heap1.c                          0x00000000   Number         0  h1_init_mt.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  fdtree.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  heap2mt.o ABSOLUTE
    ../clib/heap2.c                          0x00000000   Number         0  heap2.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc2.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc1.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  malloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc1.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  maybetermalloc2.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  heapstubs.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  init_alloc.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hguard.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  free.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  term_alloc.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxa.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludivv7m.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memclr_w.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  rt_memcpy_v6.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsnprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _snputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _sgetc.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  scanf_char.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _chval.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  __0sscanf.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf_int.o ABSOLUTE
    ../clib/scanf.c                          0x00000000   Number         0  _scanf.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_outer.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  abort.o ABSOLUTE
    ../fplib/d2f.s                           0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/ddiv.s                          0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/dflt.s                          0x00000000   Number         0  dflt_clz.o ABSOLUTE
    ../fplib/dmul.s                          0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/dnaninf.s                       0x00000000   Number         0  dnaninf.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/frnd.s                          0x00000000   Number         0  frnd.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/asinf.c                       0x00000000   Number         0  asinf.o ABSOLUTE
    ../mathlib/asinf.c                       0x00000000   Number         0  asinf_x.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f_x.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/powf.c                        0x00000000   Number         0  powf_x.o ABSOLUTE
    ../mathlib/powf.c                        0x00000000   Number         0  powf.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  roundf.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf_x.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf.o ABSOLUTE
    ..\Core\Src\dma.c                        0x00000000   Number         0  dma.o ABSOLUTE
    ..\Core\Src\gpio.c                       0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Core\Src\i2c.c                        0x00000000   Number         0  i2c.o ABSOLUTE
    ..\Core\Src\main.c                       0x00000000   Number         0  main.o ABSOLUTE
    ..\Core\Src\stm32f4xx_hal_msp.c          0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Core\Src\stm32f4xx_it.c               0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\Core\Src\system_stm32f4xx.c           0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\Core\Src\tim.c                        0x00000000   Number         0  tim.o ABSOLUTE
    ..\Core\Src\usart.c                      0x00000000   Number         0  usart.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c.c 0x00000000   Number         0  stm32f4xx_hal_i2c.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_i2c_ex.c 0x00000000   Number         0  stm32f4xx_hal_i2c_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\User\App\bno08x_app.c                 0x00000000   Number         0  bno08x_app.o ABSOLUTE
    ..\User\App\encoder_app.c                0x00000000   Number         0  encoder_app.o ABSOLUTE
    ..\User\App\gray_app.c                   0x00000000   Number         0  gray_app.o ABSOLUTE
    ..\User\App\key_app.c                    0x00000000   Number         0  key_app.o ABSOLUTE
    ..\User\App\led_app.c                    0x00000000   Number         0  led_app.o ABSOLUTE
    ..\User\App\motor_app.c                  0x00000000   Number         0  motor_app.o ABSOLUTE
    ..\User\App\oled_app.c                   0x00000000   Number         0  oled_app.o ABSOLUTE
    ..\User\App\pid_app.c                    0x00000000   Number         0  pid_app.o ABSOLUTE
    ..\User\App\usart_app.c                  0x00000000   Number         0  usart_app.o ABSOLUTE
    ..\User\Driver\key_driver.c              0x00000000   Number         0  key_driver.o ABSOLUTE
    ..\User\Driver\led_driver.c              0x00000000   Number         0  led_driver.o ABSOLUTE
    ..\User\Module\bno08x\bno08x_hal.c       0x00000000   Number         0  bno08x_hal.o ABSOLUTE
    ..\User\Module\encoder\encoder_driver.c  0x00000000   Number         0  encoder_driver.o ABSOLUTE
    ..\User\Module\grayscale\hardware_iic.c  0x00000000   Number         0  hardware_iic.o ABSOLUTE
    ..\User\Module\grayscale\software_iic.c  0x00000000   Number         0  software_iic.o ABSOLUTE
    ..\User\Module\motor\motor_driver.c      0x00000000   Number         0  motor_driver.o ABSOLUTE
    ..\User\Module\oled\oled.c               0x00000000   Number         0  oled.o ABSOLUTE
    ..\User\Module\pid\pid.c                 0x00000000   Number         0  pid.o ABSOLUTE
    ..\User\Module\ringbuffer\ringbuffer.c   0x00000000   Number         0  ringbuffer.o ABSOLUTE
    ..\User\my_scheduler.c                   0x00000000   Number         0  my_scheduler.o ABSOLUTE
    ..\User\my_timer.c                       0x00000000   Number         0  my_timer.o ABSOLUTE
    ..\\User\\App\\bno08x_app.c              0x00000000   Number         0  bno08x_app.o ABSOLUTE
    ..\\User\\App\\encoder_app.c             0x00000000   Number         0  encoder_app.o ABSOLUTE
    ..\\User\\App\\gray_app.c                0x00000000   Number         0  gray_app.o ABSOLUTE
    ..\\User\\App\\key_app.c                 0x00000000   Number         0  key_app.o ABSOLUTE
    ..\\User\\App\\led_app.c                 0x00000000   Number         0  led_app.o ABSOLUTE
    ..\\User\\App\\motor_app.c               0x00000000   Number         0  motor_app.o ABSOLUTE
    ..\\User\\App\\oled_app.c                0x00000000   Number         0  oled_app.o ABSOLUTE
    ..\\User\\App\\pid_app.c                 0x00000000   Number         0  pid_app.o ABSOLUTE
    ..\\User\\App\\usart_app.c               0x00000000   Number         0  usart_app.o ABSOLUTE
    ..\\User\\Driver\\key_driver.c           0x00000000   Number         0  key_driver.o ABSOLUTE
    ..\\User\\Driver\\led_driver.c           0x00000000   Number         0  led_driver.o ABSOLUTE
    ..\\User\\Module\\bno08x\\bno08x_hal.c   0x00000000   Number         0  bno08x_hal.o ABSOLUTE
    ..\\User\\Module\\encoder\\encoder_driver.c 0x00000000   Number         0  encoder_driver.o ABSOLUTE
    ..\\User\\Module\\grayscale\\software_iic.c 0x00000000   Number         0  software_iic.o ABSOLUTE
    ..\\User\\Module\\motor\\motor_driver.c  0x00000000   Number         0  motor_driver.o ABSOLUTE
    ..\\User\\Module\\oled\\oled.c           0x00000000   Number         0  oled.o ABSOLUTE
    ..\\User\\my_scheduler.c                 0x00000000   Number         0  my_scheduler.o ABSOLUTE
    ..\\User\\my_timer.c                     0x00000000   Number         0  my_timer.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    !!!main                                  0x08000188   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000190   Section       52  __scatter.o(!!!scatter)
    !!dczerorl2                              0x080001c4   Section       90  __dczerorl2.o(!!dczerorl2)
    !!handler_zi                             0x08000220   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x0800023c   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000001  0x0800023c   Section        6  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    .ARM.Collect$$_printf_percent$$00000002  0x08000242   Section        6  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    .ARM.Collect$$_printf_percent$$00000003  0x08000248   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000004  0x0800024e   Section        6  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    .ARM.Collect$$_printf_percent$$00000005  0x08000254   Section        6  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    .ARM.Collect$$_printf_percent$$00000006  0x0800025a   Section        6  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    .ARM.Collect$$_printf_percent$$00000007  0x08000260   Section       10  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    .ARM.Collect$$_printf_percent$$00000008  0x0800026a   Section        6  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    .ARM.Collect$$_printf_percent$$00000009  0x08000270   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x08000276   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000B  0x0800027c   Section        6  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    .ARM.Collect$$_printf_percent$$0000000C  0x08000282   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$0000000D  0x08000288   Section        6  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    .ARM.Collect$$_printf_percent$$0000000E  0x0800028e   Section        6  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    .ARM.Collect$$_printf_percent$$0000000F  0x08000294   Section        6  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    .ARM.Collect$$_printf_percent$$00000010  0x0800029a   Section        6  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    .ARM.Collect$$_printf_percent$$00000011  0x080002a0   Section        6  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    .ARM.Collect$$_printf_percent$$00000012  0x080002a6   Section       10  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    .ARM.Collect$$_printf_percent$$00000013  0x080002b0   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x080002b6   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000015  0x080002bc   Section        6  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    .ARM.Collect$$_printf_percent$$00000016  0x080002c2   Section        6  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    .ARM.Collect$$_printf_percent$$00000017  0x080002c8   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x080002cc   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000001          0x080002ce   Section        4  libinit2.o(.ARM.Collect$$libinit$$00000001)
    .ARM.Collect$$libinit$$00000004          0x080002d2   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$00000005          0x080002d2   Section        8  libinit2.o(.ARM.Collect$$libinit$$00000005)
    .ARM.Collect$$libinit$$0000000A          0x080002da   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x080002da   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x080002da   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x080002da   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x080002e0   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x080002e0   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x080002ec   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x080002ec   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x080002ec   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x080002f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x080002f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x080002f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x080002f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x080002f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x080002f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x080002f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x080002f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x080002f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x080002f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x080002f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x080002f6   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x080002f6   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x080002f8   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x080002fa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x080002fa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x080002fa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x080002fa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x080002fa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x080002fa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x080002fa   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x080002fa   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x080002fc   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x080002fc   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x080002fc   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x08000302   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x08000302   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000306   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000306   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800030e   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x08000310   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x08000310   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000314   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .emb_text                                0x0800031c   Section        0  maybetermalloc1.o(.emb_text)
    .text                                    0x0800031c   Section       64  startup_stm32f407xx.o(.text)
    $v0                                      0x0800031c   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x0800035c   Section        0  h1_alloc.o(.text)
    .text                                    0x080003ba   Section        0  h1_free.o(.text)
    .text                                    0x08000408   Section      238  lludivv7m.o(.text)
    .text                                    0x080004f8   Section        0  vsnprintf.o(.text)
    .text                                    0x0800052c   Section        0  __0sscanf.o(.text)
    .text                                    0x08000568   Section        0  _scanf_int.o(.text)
    .text                                    0x080006b4   Section        0  assert.o(.text)
    .text                                    0x08000734   Section      138  rt_memcpy_v6.o(.text)
    .text                                    0x080007be   Section       78  rt_memclr_w.o(.text)
    .text                                    0x0800080c   Section        0  heapauxi.o(.text)
    .text                                    0x08000814   Section        8  rt_heap_descriptor_intlibspace.o(.text)
    .text                                    0x0800081c   Section        0  hguard.o(.text)
    .text                                    0x08000820   Section        0  init_alloc.o(.text)
    .text                                    0x080008aa   Section        0  h1_init.o(.text)
    .text                                    0x080008b8   Section        0  _rserrno.o(.text)
    .text                                    0x080008ce   Section        0  _printf_pad.o(.text)
    .text                                    0x0800091c   Section        0  _printf_truncate.o(.text)
    .text                                    0x08000940   Section        0  _printf_str.o(.text)
    .text                                    0x08000994   Section        0  _printf_dec.o(.text)
    .text                                    0x08000a0c   Section        0  _printf_charcount.o(.text)
    .text                                    0x08000a34   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08000a35   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08000a64   Section        0  _sputc.o(.text)
    .text                                    0x08000a6e   Section        0  _snputc.o(.text)
    .text                                    0x08000a80   Section        0  _printf_wctomb.o(.text)
    .text                                    0x08000b3c   Section        0  _printf_longlong_dec.o(.text)
    .text                                    0x08000bb8   Section        0  _printf_oct_int_ll.o(.text)
    _printf_longlong_oct_internal            0x08000bb9   Thumb Code     0  _printf_oct_int_ll.o(.text)
    .text                                    0x08000c28   Section        0  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_common                       0x08000c29   Thumb Code     0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x08000cbc   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x08000e44   Section        0  _chval.o(.text)
    .text                                    0x08000e60   Section        0  scanf_char.o(.text)
    _scanf_char_input                        0x08000e61   Thumb Code    12  scanf_char.o(.text)
    .text                                    0x08000e8c   Section        0  _sgetc.o(.text)
    .text                                    0x08000ecc   Section        0  abort.o(.text)
    .text                                    0x08000ee2   Section        0  assert_puts.o(.text)
    .text                                    0x08000ef6   Section      100  rt_memcpy_w.o(.text)
    .text                                    0x08000f5a   Section        0  sys_wrch.o(.text)
    .text                                    0x08000f68   Section        0  sys_exit.o(.text)
    .text                                    0x08000f74   Section        8  libspace.o(.text)
    .text                                    0x08000f7c   Section        8  rt_errno_addr_intlibspace.o(.text)
    .text                                    0x08000f84   Section        0  h1_extend.o(.text)
    .text                                    0x08000fb8   Section      138  lludiv10.o(.text)
    .text                                    0x08001042   Section        0  isspace.o(.text)
    .text                                    0x08001054   Section        0  _printf_intcommon.o(.text)
    .text                                    0x08001106   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x08001109   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08001524   Section        0  _printf_fp_hex.o(.text)
    .text                                    0x08001820   Section        0  _printf_char.o(.text)
    .text                                    0x0800184c   Section        0  _printf_wchar.o(.text)
    .text                                    0x08001878   Section        0  _scanf.o(.text)
    .text                                    0x08001bec   Section        0  _wcrtomb.o(.text)
    .text                                    0x08001c2c   Section        0  defsig_abrt_outer.o(.text)
    .text                                    0x08001c3a   Section        0  defsig_rtmem_outer.o(.text)
    .text                                    0x08001c48   Section        2  use_no_semi.o(.text)
    .text                                    0x08001c4a   Section        0  indicate_semi.o(.text)
    .text                                    0x08001c4a   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x08001c94   Section       16  rt_ctype_table.o(.text)
    .text                                    0x08001ca4   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x08001cac   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08001d2c   Section        0  bigflt0.o(.text)
    .text                                    0x08001e10   Section        0  exit.o(.text)
    .text                                    0x08001e22   Section        0  defsig_exit.o(.text)
    .text                                    0x08001e2c   Section        0  defsig_abrt_inner.o(.text)
    .text                                    0x08001e5c   Section        0  defsig_rtmem_inner.o(.text)
    .text                                    0x08001eac   Section        0  defsig_general.o(.text)
    .text                                    0x08001ee0   Section      128  strcmpv7m.o(.text)
    CL$$btod_d2e                             0x08001f60   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x08001f9e   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08001fe4   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08002044   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x0800237c   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08002458   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x08002482   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x080024ac   Section      580  btod.o(CL$$btod_mult_common)
    i.Angle_PID_control                      0x080026f0   Section        0  pid_app.o(i.Angle_PID_control)
    i.BNO080_HardwareReset                   0x08002784   Section        0  bno08x_hal.o(i.BNO080_HardwareReset)
    i.BNO080_Init                            0x08002800   Section        0  bno08x_hal.o(i.BNO080_Init)
    i.BusFault_Handler                       0x08002814   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.Car_State_Update                       0x08002818   Section        0  my_timer.o(i.Car_State_Update)
    i.DMA1_Stream0_IRQHandler                0x08002990   Section        0  stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler)
    i.DMA2_Stream2_IRQHandler                0x080029a0   Section        0  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    i.DMA_CalcBaseAndBitshift                0x080029b0   Section        0  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    DMA_CalcBaseAndBitshift                  0x080029b1   Thumb Code    46  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    i.DMA_CheckFifoParam                     0x080029e4   Section        0  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    DMA_CheckFifoParam                       0x080029e5   Thumb Code   170  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    i.DMA_SetConfig                          0x08002a8e   Section        0  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x08002a8f   Thumb Code    44  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    i.DRV8871_Control                        0x08002aba   Section        0  motor_driver.o(i.DRV8871_Control)
    DRV8871_Control                          0x08002abb   Thumb Code   224  motor_driver.o(i.DRV8871_Control)
    i.DebugMon_Handler                       0x08002b9a   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Delay_us                               0x08002b9c   Section        0  software_iic.o(i.Delay_us)
    i.EXTI3_IRQHandler                       0x08002bf4   Section        0  stm32f4xx_it.o(i.EXTI3_IRQHandler)
    i.Encoder_Driver_Init                    0x08002c00   Section        0  encoder_driver.o(i.Encoder_Driver_Init)
    i.Encoder_Driver_Update                  0x08002c30   Section        0  encoder_driver.o(i.Encoder_Driver_Update)
    i.Encoder_Init                           0x08002cc0   Section        0  encoder_app.o(i.Encoder_Init)
    i.Encoder_Task                           0x08002ce8   Section        0  encoder_app.o(i.Encoder_Task)
    i.Error_Handler                          0x08002d00   Section        0  main.o(i.Error_Handler)
    i.Float_To_Speed1000                     0x08002d06   Section        0  motor_driver.o(i.Float_To_Speed1000)
    Float_To_Speed1000                       0x08002d07   Thumb Code    42  motor_driver.o(i.Float_To_Speed1000)
    i.Gray_Init                              0x08002d30   Section        0  gray_app.o(i.Gray_Init)
    i.Gray_Task                              0x08002d9c   Section        0  gray_app.o(i.Gray_Task)
    i.HAL_DMA_Abort                          0x08002e18   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x08002ec4   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_IRQHandler                     0x08002eec   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x0800312c   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x08003218   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_Delay                              0x080032ac   Section        0  stm32f4xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_EXTI_Callback                 0x080032d4   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback)
    i.HAL_GPIO_EXTI_IRQHandler               0x080032d8   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler)
    i.HAL_GPIO_Init                          0x080032f4   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GPIO_ReadPin                       0x080034e8   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    i.HAL_GPIO_WritePin                      0x080034f8   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    i.HAL_GetTick                            0x08003504   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_I2C_Init                           0x08003510   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Init)
    i.HAL_I2C_Master_Receive                 0x080036e0   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive)
    i.HAL_I2C_Master_Transmit                0x080039ec   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit)
    i.HAL_I2C_Mem_Write                      0x08003b5c   Section        0  stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write)
    i.HAL_I2C_MspInit                        0x08003cc0   Section        0  i2c.o(i.HAL_I2C_MspInit)
    i.HAL_IncTick                            0x08003db4   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08003dcc   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08003e08   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08003e54   Section        0  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08003ea4   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08003ecc   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08003f48   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x08003f70   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetHCLKFreq                    0x080040f4   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    i.HAL_RCC_GetPCLK1Freq                   0x08004100   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08004120   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08004140   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x080041f0   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x0800468c   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_TIMEx_BreakCallback                0x080046c0   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    i.HAL_TIMEx_CommutCallback               0x080046c2   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    i.HAL_TIMEx_ConfigBreakDeadTime          0x080046c4   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    i.HAL_TIMEx_MasterConfigSynchronization  0x08004738   Section        0  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    i.HAL_TIM_Base_Init                      0x080047ec   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    i.HAL_TIM_Base_MspInit                   0x08004854   Section        0  tim.o(i.HAL_TIM_Base_MspInit)
    i.HAL_TIM_Base_Start_IT                  0x080048c8   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    i.HAL_TIM_ConfigClockSource              0x08004970   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    i.HAL_TIM_Encoder_Init                   0x08004a7c   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    i.HAL_TIM_Encoder_MspInit                0x08004b44   Section        0  tim.o(i.HAL_TIM_Encoder_MspInit)
    i.HAL_TIM_Encoder_Start                  0x08004c3c   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start)
    i.HAL_TIM_IC_CaptureCallback             0x08004d08   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    i.HAL_TIM_IRQHandler                     0x08004d0a   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    i.HAL_TIM_MspPostInit                    0x08004e78   Section        0  tim.o(i.HAL_TIM_MspPostInit)
    i.HAL_TIM_OC_DelayElapsedCallback        0x08004edc   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    i.HAL_TIM_PWM_ConfigChannel              0x08004ede   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    i.HAL_TIM_PWM_Init                       0x08004fe2   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    i.HAL_TIM_PWM_MspInit                    0x08005048   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    i.HAL_TIM_PWM_PulseFinishedCallback      0x0800504a   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    i.HAL_TIM_PWM_Start                      0x0800504c   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    i.HAL_TIM_PeriodElapsedCallback          0x08005158   Section        0  my_timer.o(i.HAL_TIM_PeriodElapsedCallback)
    i.HAL_TIM_TriggerCallback                0x0800529c   Section        0  stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    i.HAL_UARTEx_ReceiveToIdle_DMA           0x0800529e   Section        0  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    i.HAL_UARTEx_RxEventCallback             0x08005310   Section        0  usart_app.o(i.HAL_UARTEx_RxEventCallback)
    i.HAL_UART_DMAStop                       0x08005368   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop)
    i.HAL_UART_ErrorCallback                 0x080053f2   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x080053f4   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x080056fc   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08005774   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_RxCpltCallback                0x08005954   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_RxHalfCpltCallback            0x08005956   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    i.HAL_UART_Transmit                      0x08005958   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    i.HAL_UART_TxCpltCallback                0x08005a16   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HardFault_Handler                      0x08005a18   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.I2C_IsAcknowledgeFailed                0x08005a1c   Section        0  stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    I2C_IsAcknowledgeFailed                  0x08005a1d   Thumb Code    62  stm32f4xx_hal_i2c.o(i.I2C_IsAcknowledgeFailed)
    i.I2C_MasterRequestRead                  0x08005a5c   Section        0  stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead)
    I2C_MasterRequestRead                    0x08005a5d   Thumb Code   308  stm32f4xx_hal_i2c.o(i.I2C_MasterRequestRead)
    i.I2C_MasterRequestWrite                 0x08005b94   Section        0  stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite)
    I2C_MasterRequestWrite                   0x08005b95   Thumb Code   186  stm32f4xx_hal_i2c.o(i.I2C_MasterRequestWrite)
    i.I2C_RequestMemoryWrite                 0x08005c54   Section        0  stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite)
    I2C_RequestMemoryWrite                   0x08005c55   Thumb Code   220  stm32f4xx_hal_i2c.o(i.I2C_RequestMemoryWrite)
    i.I2C_WaitOnBTFFlagUntilTimeout          0x08005d34   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    I2C_WaitOnBTFFlagUntilTimeout            0x08005d35   Thumb Code   102  stm32f4xx_hal_i2c.o(i.I2C_WaitOnBTFFlagUntilTimeout)
    i.I2C_WaitOnFlagUntilTimeout             0x08005d9a   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    I2C_WaitOnFlagUntilTimeout               0x08005d9b   Thumb Code   190  stm32f4xx_hal_i2c.o(i.I2C_WaitOnFlagUntilTimeout)
    i.I2C_WaitOnMasterAddressFlagUntilTimeout 0x08005e58   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    I2C_WaitOnMasterAddressFlagUntilTimeout  0x08005e59   Thumb Code   250  stm32f4xx_hal_i2c.o(i.I2C_WaitOnMasterAddressFlagUntilTimeout)
    i.I2C_WaitOnRXNEFlagUntilTimeout         0x08005f52   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout)
    I2C_WaitOnRXNEFlagUntilTimeout           0x08005f53   Thumb Code   138  stm32f4xx_hal_i2c.o(i.I2C_WaitOnRXNEFlagUntilTimeout)
    i.I2C_WaitOnTXEFlagUntilTimeout          0x08005fdc   Section        0  stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    I2C_WaitOnTXEFlagUntilTimeout            0x08005fdd   Thumb Code   102  stm32f4xx_hal_i2c.o(i.I2C_WaitOnTXEFlagUntilTimeout)
    i.IIC_Delay                              0x08006042   Section        0  software_iic.o(i.IIC_Delay)
    IIC_Delay                                0x08006043   Thumb Code    10  software_iic.o(i.IIC_Delay)
    i.IIC_Get_Digtal                         0x0800604c   Section        0  software_iic.o(i.IIC_Get_Digtal)
    i.IIC_ReadBytes                          0x0800606c   Section        0  software_iic.o(i.IIC_ReadBytes)
    i.IIC_RecvByte                           0x080060e4   Section        0  software_iic.o(i.IIC_RecvByte)
    i.IIC_SendAck                            0x08006160   Section        0  software_iic.o(i.IIC_SendAck)
    i.IIC_SendByte                           0x08006194   Section        0  software_iic.o(i.IIC_SendByte)
    i.IIC_SendNAck                           0x080061ec   Section        0  software_iic.o(i.IIC_SendNAck)
    i.IIC_Start                              0x08006218   Section        0  software_iic.o(i.IIC_Start)
    i.IIC_Stop                               0x08006250   Section        0  software_iic.o(i.IIC_Stop)
    i.IIC_WaitAck                            0x08006284   Section        0  software_iic.o(i.IIC_WaitAck)
    i.Line_PID_control                       0x080062c0   Section        0  pid_app.o(i.Line_PID_control)
    i.MX_DMA_Init                            0x08006348   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_GPIO_Init                           0x080063b0   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_I2C1_Init                           0x0800657c   Section        0  i2c.o(i.MX_I2C1_Init)
    i.MX_I2C2_Init                           0x080065b8   Section        0  i2c.o(i.MX_I2C2_Init)
    i.MX_TIM1_Init                           0x080065f4   Section        0  tim.o(i.MX_TIM1_Init)
    i.MX_TIM2_Init                           0x080066fc   Section        0  tim.o(i.MX_TIM2_Init)
    i.MX_TIM3_Init                           0x08006768   Section        0  tim.o(i.MX_TIM3_Init)
    i.MX_TIM4_Init                           0x080067e8   Section        0  tim.o(i.MX_TIM4_Init)
    i.MX_UART5_Init                          0x08006868   Section        0  usart.o(i.MX_UART5_Init)
    i.MX_USART1_UART_Init                    0x080068a0   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MemManage_Handler                      0x080068d8   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.Motor_Create                           0x080068dc   Section        0  motor_driver.o(i.Motor_Create)
    i.Motor_Init                             0x080069a0   Section        0  motor_app.o(i.Motor_Init)
    i.Motor_SetSpeed                         0x08006a00   Section        0  motor_driver.o(i.Motor_SetSpeed)
    i.Motor_ValidateFloatSpeed               0x08006aa0   Section        0  motor_driver.o(i.Motor_ValidateFloatSpeed)
    Motor_ValidateFloatSpeed                 0x08006aa1   Thumb Code    38  motor_driver.o(i.Motor_ValidateFloatSpeed)
    i.Motor_ValidateParams                   0x08006ad0   Section        0  motor_driver.o(i.Motor_ValidateParams)
    Motor_ValidateParams                     0x08006ad1   Thumb Code    26  motor_driver.o(i.Motor_ValidateParams)
    i.NMI_Handler                            0x08006aea   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.OLED_Clear                             0x08006aee   Section        0  oled.o(i.OLED_Clear)
    i.OLED_Init                              0x08006b28   Section        0  oled.o(i.OLED_Init)
    i.OLED_Set_Position                      0x08006b58   Section        0  oled.o(i.OLED_Set_Position)
    i.OLED_ShowChar                          0x08006b7c   Section        0  oled.o(i.OLED_ShowChar)
    i.OLED_ShowStr                           0x08006c18   Section        0  oled.o(i.OLED_ShowStr)
    i.OLED_Write_cmd                         0x08006c54   Section        0  oled.o(i.OLED_Write_cmd)
    i.OLED_Write_data                        0x08006c78   Section        0  oled.o(i.OLED_Write_data)
    i.PID_Init                               0x08006c9c   Section        0  pid_app.o(i.PID_Init)
    i.PID_Task                               0x08006dac   Section        0  pid_app.o(i.PID_Task)
    i.PendSV_Handler                         0x08006f00   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.Ping                                   0x08006f02   Section        0  software_iic.o(i.Ping)
    i.QuaternionToEulerAngles                0x08006f20   Section        0  bno08x_hal.o(i.QuaternionToEulerAngles)
    i.SVC_Handler                            0x08007114   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.Set_Pin_Mode                           0x08007116   Section        0  motor_driver.o(i.Set_Pin_Mode)
    Set_Pin_Mode                             0x08007117   Thumb Code   102  motor_driver.o(i.Set_Pin_Mode)
    i.Speed1000_To_PWM                       0x0800717c   Section        0  motor_driver.o(i.Speed1000_To_PWM)
    Speed1000_To_PWM                         0x0800717d   Thumb Code    88  motor_driver.o(i.Speed1000_To_PWM)
    i.SysTick_Handler                        0x080071d4   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x080071dc   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08007290   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TIM2_IRQHandler                        0x080072a4   Section        0  stm32f4xx_it.o(i.TIM2_IRQHandler)
    i.TIM_Base_SetConfig                     0x080072b4   Section        0  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    i.TIM_CCxChannelCmd                      0x08007394   Section        0  stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    i.TIM_ETR_SetConfig                      0x080073b6   Section        0  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    i.TIM_ITRx_SetConfig                     0x080073cc   Section        0  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    TIM_ITRx_SetConfig                       0x080073cd   Thumb Code    18  stm32f4xx_hal_tim.o(i.TIM_ITRx_SetConfig)
    i.TIM_OC1_SetConfig                      0x080073e0   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    TIM_OC1_SetConfig                        0x080073e1   Thumb Code   104  stm32f4xx_hal_tim.o(i.TIM_OC1_SetConfig)
    i.TIM_OC2_SetConfig                      0x08007450   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    i.TIM_OC3_SetConfig                      0x080074cc   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    TIM_OC3_SetConfig                        0x080074cd   Thumb Code   112  stm32f4xx_hal_tim.o(i.TIM_OC3_SetConfig)
    i.TIM_OC4_SetConfig                      0x08007544   Section        0  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    TIM_OC4_SetConfig                        0x08007545   Thumb Code    74  stm32f4xx_hal_tim.o(i.TIM_OC4_SetConfig)
    i.TIM_TI1_ConfigInputStage               0x08007598   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    TIM_TI1_ConfigInputStage                 0x08007599   Thumb Code    38  stm32f4xx_hal_tim.o(i.TIM_TI1_ConfigInputStage)
    i.TIM_TI2_ConfigInputStage               0x080075be   Section        0  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    TIM_TI2_ConfigInputStage                 0x080075bf   Thumb Code    40  stm32f4xx_hal_tim.o(i.TIM_TI2_ConfigInputStage)
    i.UART5_IRQHandler                       0x080075e8   Section        0  stm32f4xx_it.o(i.UART5_IRQHandler)
    i.UART_DMAAbortOnError                   0x080075f8   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x080075f9   Thumb Code    18  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_DMAError                          0x0800760a   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAError)
    UART_DMAError                            0x0800760b   Thumb Code    80  stm32f4xx_hal_uart.o(i.UART_DMAError)
    i.UART_DMAReceiveCplt                    0x0800765a   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    UART_DMAReceiveCplt                      0x0800765b   Thumb Code   180  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    i.UART_DMARxHalfCplt                     0x0800770e   Section        0  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    UART_DMARxHalfCplt                       0x0800770f   Thumb Code    36  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    i.UART_EndRxTransfer                     0x08007732   Section        0  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08007733   Thumb Code   108  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTransmit_IT                    0x0800779e   Section        0  stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT)
    UART_EndTransmit_IT                      0x0800779f   Thumb Code    32  stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT)
    i.UART_EndTxTransfer                     0x080077be   Section        0  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    UART_EndTxTransfer                       0x080077bf   Thumb Code    38  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    i.UART_Receive_IT                        0x080077e4   Section        0  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x080077e5   Thumb Code   252  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x080078e0   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x080078e1   Thumb Code   546  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Start_Receive_DMA                 0x08007b0c   Section        0  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    i.UART_Transmit_IT                       0x08007be4   Section        0  stm32f4xx_hal_uart.o(i.UART_Transmit_IT)
    UART_Transmit_IT                         0x08007be5   Thumb Code    96  stm32f4xx_hal_uart.o(i.UART_Transmit_IT)
    i.UART_WaitOnFlagUntilTimeout            0x08007c44   Section        0  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    UART_WaitOnFlagUntilTimeout              0x08007c45   Thumb Code   140  stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout)
    i.USART1_IRQHandler                      0x08007cd0   Section        0  stm32f4xx_it.o(i.USART1_IRQHandler)
    i.UsageFault_Handler                     0x08007ce0   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__ARM_fpclassify                       0x08007ce4   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__ARM_fpclassifyf                      0x08007d14   Section        0  fpclassifyf.o(i.__ARM_fpclassifyf)
    i.__NVIC_GetPriorityGrouping             0x08007d3c   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping)
    __NVIC_GetPriorityGrouping               0x08007d3d   Thumb Code    10  stm32f4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping)
    i.__NVIC_SetPriority                     0x08007d4c   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08007d4d   Thumb Code    32  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__hardfp_asinf                         0x08007d74   Section        0  asinf.o(i.__hardfp_asinf)
    i.__hardfp_atan2f                        0x08007ea0   Section        0  atan2f.o(i.__hardfp_atan2f)
    i.__hardfp_powf                          0x0800814c   Section        0  powf.o(i.__hardfp_powf)
    i.__hardfp_roundf                        0x080087b0   Section        0  roundf.o(i.__hardfp_roundf)
    i.__hardfp_sqrtf                         0x0800884a   Section        0  sqrtf.o(i.__hardfp_sqrtf)
    i.__mathlib_flt_divzero                  0x08008884   Section        0  funder.o(i.__mathlib_flt_divzero)
    i.__mathlib_flt_infnan                   0x08008898   Section        0  funder.o(i.__mathlib_flt_infnan)
    i.__mathlib_flt_infnan2                  0x0800889e   Section        0  funder.o(i.__mathlib_flt_infnan2)
    i.__mathlib_flt_invalid                  0x080088a4   Section        0  funder.o(i.__mathlib_flt_invalid)
    i.__mathlib_flt_overflow                 0x080088b4   Section        0  funder.o(i.__mathlib_flt_overflow)
    i.__mathlib_flt_underflow                0x080088c4   Section        0  funder.o(i.__mathlib_flt_underflow)
    i._is_digit                              0x080088d4   Section        0  __printf_wp.o(i._is_digit)
    i.all_task_init                          0x080088e4   Section        0  my_scheduler.o(i.all_task_init)
    i.all_task_run                           0x08008918   Section        0  my_scheduler.o(i.all_task_run)
    i.bno080_task                            0x08008970   Section        0  bno08x_app.o(i.bno080_task)
    i.convert_to_continuous_yaw              0x08008a00   Section        0  bno08x_app.o(i.convert_to_continuous_yaw)
    i.dataAvailable                          0x08008a98   Section        0  bno08x_hal.o(i.dataAvailable)
    i.enableRotationVector                   0x08008ac8   Section        0  bno08x_hal.o(i.enableRotationVector)
    i.getQuatI                               0x08008ad8   Section        0  bno08x_hal.o(i.getQuatI)
    i.getQuatJ                               0x08008af4   Section        0  bno08x_hal.o(i.getQuatJ)
    i.getQuatK                               0x08008b10   Section        0  bno08x_hal.o(i.getQuatK)
    i.getQuatReal                            0x08008b2c   Section        0  bno08x_hal.o(i.getQuatReal)
    i.get_yaw                                0x08008b48   Section        0  bno08x_app.o(i.get_yaw)
    i.key_read                               0x08008b64   Section        0  key_driver.o(i.key_read)
    i.key_task                               0x08008bb0   Section        0  key_app.o(i.key_task)
    i.led_disp                               0x08008c3c   Section        0  led_driver.o(i.led_disp)
    i.led_init                               0x08008cc0   Section        0  led_app.o(i.led_init)
    i.led_task                               0x08008cc4   Section        0  led_app.o(i.led_task)
    i.main                                   0x08008cd4   Section        0  main.o(i.main)
    i.motor_set_l                            0x08008d10   Section        0  motor_app.o(i.motor_set_l)
    i.motor_set_r                            0x08008d30   Section        0  motor_app.o(i.motor_set_r)
    i.my_bno080_init                         0x08008d50   Section        0  bno08x_app.o(i.my_bno080_init)
    i.my_oled_init                           0x08008e74   Section        0  oled_app.o(i.my_oled_init)
    i.my_printf                              0x08008e7c   Section        0  usart_app.o(i.my_printf)
    i.oled_printf                            0x08008eb6   Section        0  oled_app.o(i.oled_printf)
    i.oled_task                              0x08008ef4   Section        0  oled_app.o(i.oled_task)
    i.parseInputReport                       0x08008fb8   Section        0  bno08x_hal.o(i.parseInputReport)
    i.pid_calculate_positional               0x08009198   Section        0  pid.o(i.pid_calculate_positional)
    i.pid_constrain                          0x080091be   Section        0  pid.o(i.pid_constrain)
    i.pid_formula_positional                 0x080091e8   Section        0  pid.o(i.pid_formula_positional)
    pid_formula_positional                   0x080091e9   Thumb Code   122  pid.o(i.pid_formula_positional)
    i.pid_init                               0x08009264   Section        0  pid.o(i.pid_init)
    i.pid_out_limit                          0x080092b8   Section        0  pid.o(i.pid_out_limit)
    pid_out_limit                            0x080092b9   Thumb Code    64  pid.o(i.pid_out_limit)
    i.pid_reset                              0x080092f8   Section        0  pid.o(i.pid_reset)
    i.pid_set_target                         0x08009338   Section        0  pid.o(i.pid_set_target)
    i.qToFloat                               0x0800933e   Section        0  bno08x_hal.o(i.qToFloat)
    i.receivePacket                          0x08009378   Section        0  bno08x_hal.o(i.receivePacket)
    receivePacket                            0x08009379   Thumb Code   178  bno08x_hal.o(i.receivePacket)
    i.rt_ringbuffer_data_len                 0x0800943c   Section        0  ringbuffer.o(i.rt_ringbuffer_data_len)
    i.rt_ringbuffer_get                      0x08009478   Section        0  ringbuffer.o(i.rt_ringbuffer_get)
    i.rt_ringbuffer_init                     0x08009564   Section        0  ringbuffer.o(i.rt_ringbuffer_init)
    i.rt_ringbuffer_put                      0x080095f8   Section        0  ringbuffer.o(i.rt_ringbuffer_put)
    i.rt_ringbuffer_status                   0x080096e8   Section        0  ringbuffer.o(i.rt_ringbuffer_status)
    rt_ringbuffer_status                     0x080096e9   Thumb Code    42  ringbuffer.o(i.rt_ringbuffer_status)
    i.sendPacket                             0x08009714   Section        0  bno08x_hal.o(i.sendPacket)
    sendPacket                               0x08009715   Thumb Code   112  bno08x_hal.o(i.sendPacket)
    i.setFeatureCommand                      0x08009794   Section        0  bno08x_hal.o(i.setFeatureCommand)
    i.softReset                              0x080097f0   Section        0  bno08x_hal.o(i.softReset)
    i.sqrtf                                  0x08009828   Section        0  sqrtf.o(i.sqrtf)
    i.timer_init                             0x08009868   Section        0  my_timer.o(i.timer_init)
    i.uart_init                              0x08009878   Section        0  usart_app.o(i.uart_init)
    i.uart_task                              0x080098b4   Section        0  usart_app.o(i.uart_task)
    locale$$code                             0x080099ac   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x080099d8   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$d2f                                0x08009a04   Section       98  d2f.o(x$fpl$d2f)
    $v0                                      0x08009a04   Number         0  d2f.o(x$fpl$d2f)
    x$fpl$ddiv                               0x08009a68   Section      688  ddiv.o(x$fpl$ddiv)
    $v0                                      0x08009a68   Number         0  ddiv.o(x$fpl$ddiv)
    ddiv_entry                               0x08009a6f   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    x$fpl$dflt                               0x08009d18   Section       46  dflt_clz.o(x$fpl$dflt)
    $v0                                      0x08009d18   Number         0  dflt_clz.o(x$fpl$dflt)
    x$fpl$dmul                               0x08009d48   Section      340  dmul.o(x$fpl$dmul)
    $v0                                      0x08009d48   Number         0  dmul.o(x$fpl$dmul)
    x$fpl$dnaninf                            0x08009e9c   Section      156  dnaninf.o(x$fpl$dnaninf)
    $v0                                      0x08009e9c   Number         0  dnaninf.o(x$fpl$dnaninf)
    x$fpl$dretinf                            0x08009f38   Section       12  dretinf.o(x$fpl$dretinf)
    $v0                                      0x08009f38   Number         0  dretinf.o(x$fpl$dretinf)
    x$fpl$f2d                                0x08009f44   Section       86  f2d.o(x$fpl$f2d)
    $v0                                      0x08009f44   Number         0  f2d.o(x$fpl$f2d)
    x$fpl$fnaninf                            0x08009f9a   Section      140  fnaninf.o(x$fpl$fnaninf)
    $v0                                      0x08009f9a   Number         0  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fpinit                             0x0800a026   Section       10  fpinit.o(x$fpl$fpinit)
    $v0                                      0x0800a026   Number         0  fpinit.o(x$fpl$fpinit)
    x$fpl$fretinf                            0x0800a030   Section       10  fretinf.o(x$fpl$fretinf)
    $v0                                      0x0800a030   Number         0  fretinf.o(x$fpl$fretinf)
    x$fpl$frnd                               0x0800a03c   Section       96  frnd.o(x$fpl$frnd)
    $v0                                      0x0800a03c   Number         0  frnd.o(x$fpl$frnd)
    x$fpl$printf1                            0x0800a09c   Section        4  printf1.o(x$fpl$printf1)
    $v0                                      0x0800a09c   Number         0  printf1.o(x$fpl$printf1)
    x$fpl$printf2                            0x0800a0a0   Section        4  printf2.o(x$fpl$printf2)
    $v0                                      0x0800a0a0   Number         0  printf2.o(x$fpl$printf2)
    .constdata                               0x0800a0a4   Section        8  stm32f4xx_hal_dma.o(.constdata)
    x$fpl$usenofp                            0x0800a0a4   Section        0  usenofp.o(x$fpl$usenofp)
    flagBitshiftOffset                       0x0800a0a4   Data           8  stm32f4xx_hal_dma.o(.constdata)
    .constdata                               0x0800a0ac   Section       24  system_stm32f4xx.o(.constdata)
    .constdata                               0x0800a0c4   Section     2712  oled.o(.constdata)
    F8X16                                    0x0800a2ec   Data        1520  oled.o(.constdata)
    .constdata                               0x0800ab5c   Section      320  powf.o(.constdata)
    table                                    0x0800ab5c   Data         128  powf.o(.constdata)
    powersof2to1over16top                    0x0800abdc   Data          64  powf.o(.constdata)
    powersof2to1over16bot                    0x0800ac1c   Data          64  powf.o(.constdata)
    powersof2to1over16all                    0x0800ac5c   Data          64  powf.o(.constdata)
    .constdata                               0x0800ac9c   Section        8  _printf_wctomb.o(.constdata)
    initial_mbstate                          0x0800ac9c   Data           8  _printf_wctomb.o(.constdata)
    .constdata                               0x0800aca4   Section       40  _printf_hex_int_ll_ptr.o(.constdata)
    uc_hextab                                0x0800aca4   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    lc_hextab                                0x0800acb8   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    .constdata                               0x0800accc   Section       17  __printf_flags_ss_wp.o(.constdata)
    maptable                                 0x0800accc   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x0800acdd   Section       38  _printf_fp_hex.o(.constdata)
    lc_hextab                                0x0800acdd   Data          19  _printf_fp_hex.o(.constdata)
    uc_hextab                                0x0800acf0   Data          19  _printf_fp_hex.o(.constdata)
    .constdata                               0x0800ad04   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x0800ad04   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x0800ad40   Data          64  bigflt0.o(.constdata)
    locale$$data                             0x0800adb8   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x0800adbc   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x0800adc4   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x0800add0   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x0800add2   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x0800add3   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x0800add4   Section      272  lc_ctype_c.o(locale$$data)
    __lcnum_c_end                            0x0800add4   Data           0  lc_numeric_c.o(locale$$data)
    __lcctype_c_name                         0x0800add8   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x0800ade0   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x0800aee4   Data           0  lc_ctype_c.o(locale$$data)
    .data                                    0x20000000   Section        9  stm32f4xx_hal.o(.data)
    .data                                    0x2000000c   Section        4  system_stm32f4xx.o(.data)
    .data                                    0x20000010   Section       22  oled.o(.data)
    .data                                    0x20000028   Section       88  bno08x_hal.o(.data)
    hi2c_bno080                              0x20000028   Data           4  bno08x_hal.o(.data)
    _deviceAddress                           0x2000002c   Data           1  bno08x_hal.o(.data)
    shtpHeader                               0x2000002d   Data           4  bno08x_hal.o(.data)
    sequenceNumber                           0x20000031   Data           6  bno08x_hal.o(.data)
    commandSequenceNumber                    0x20000037   Data           1  bno08x_hal.o(.data)
    rawAccelX                                0x20000038   Data           2  bno08x_hal.o(.data)
    rawAccelY                                0x2000003a   Data           2  bno08x_hal.o(.data)
    rawAccelZ                                0x2000003c   Data           2  bno08x_hal.o(.data)
    accelAccuracy                            0x2000003e   Data           1  bno08x_hal.o(.data)
    rawLinAccelX                             0x20000040   Data           2  bno08x_hal.o(.data)
    rawLinAccelY                             0x20000042   Data           2  bno08x_hal.o(.data)
    rawLinAccelZ                             0x20000044   Data           2  bno08x_hal.o(.data)
    accelLinAccuracy                         0x20000046   Data           1  bno08x_hal.o(.data)
    rawGyroX                                 0x20000048   Data           2  bno08x_hal.o(.data)
    rawGyroY                                 0x2000004a   Data           2  bno08x_hal.o(.data)
    rawGyroZ                                 0x2000004c   Data           2  bno08x_hal.o(.data)
    gyroAccuracy                             0x2000004e   Data           1  bno08x_hal.o(.data)
    rawMagX                                  0x20000050   Data           2  bno08x_hal.o(.data)
    rawMagY                                  0x20000052   Data           2  bno08x_hal.o(.data)
    rawMagZ                                  0x20000054   Data           2  bno08x_hal.o(.data)
    magAccuracy                              0x20000056   Data           1  bno08x_hal.o(.data)
    rawQuatI                                 0x20000058   Data           2  bno08x_hal.o(.data)
    rawQuatJ                                 0x2000005a   Data           2  bno08x_hal.o(.data)
    rawQuatK                                 0x2000005c   Data           2  bno08x_hal.o(.data)
    rawQuatReal                              0x2000005e   Data           2  bno08x_hal.o(.data)
    rawQuatRadianAccuracy                    0x20000060   Data           2  bno08x_hal.o(.data)
    quatAccuracy                             0x20000062   Data           1  bno08x_hal.o(.data)
    stepCount                                0x20000064   Data           2  bno08x_hal.o(.data)
    stabilityClassifier                      0x20000066   Data           1  bno08x_hal.o(.data)
    activityClassifier                       0x20000067   Data           1  bno08x_hal.o(.data)
    _activityConfidences                     0x20000068   Data           4  bno08x_hal.o(.data)
    rotationVector_Q1                        0x2000006c   Data           4  bno08x_hal.o(.data)
    accelerometer_Q1                         0x20000070   Data           4  bno08x_hal.o(.data)
    linear_accelerometer_Q1                  0x20000074   Data           4  bno08x_hal.o(.data)
    gyro_Q1                                  0x20000078   Data           4  bno08x_hal.o(.data)
    magnetometer_Q1                          0x2000007c   Data           4  bno08x_hal.o(.data)
    .data                                    0x20000080   Section        1  led_driver.o(.data)
    temp_old                                 0x20000080   Data           1  led_driver.o(.data)
    .data                                    0x20000081   Section        5  led_app.o(.data)
    .data                                    0x20000086   Section        6  key_app.o(.data)
    key_old                                  0x2000008b   Data           1  key_app.o(.data)
    .data                                    0x2000008c   Section       88  pid_app.o(.data)
    .data                                    0x200000e4   Section       40  gray_app.o(.data)
    .data                                    0x2000010c   Section       29  bno08x_app.o(.data)
    .data                                    0x2000012c   Section       49  my_scheduler.o(.data)
    .data                                    0x20000160   Section       28  my_timer.o(.data)
    .bss                                     0x2000017c   Section      168  i2c.o(.bss)
    .bss                                     0x20000224   Section      288  tim.o(.bss)
    .bss                                     0x20000344   Section      336  usart.o(.bss)
    .bss                                     0x20000494   Section      174  bno08x_hal.o(.bss)
    shtpData                                 0x20000494   Data         128  bno08x_hal.o(.bss)
    metaData                                 0x20000514   Data          36  bno08x_hal.o(.bss)
    activityConfidences                      0x20000538   Data          10  bno08x_hal.o(.bss)
    .bss                                     0x20000544   Section      396  usart_app.o(.bss)
    .bss                                     0x200006d0   Section       96  motor_app.o(.bss)
    .bss                                     0x20000730   Section       32  encoder_app.o(.bss)
    .bss                                     0x20000750   Section      240  pid_app.o(.bss)
    .bss                                     0x20000840   Section       96  libspace.o(.bss)
    HEAP                                     0x200008a0   Section     4096  startup_stm32f407xx.o(HEAP)
    Heap_Mem                                 0x200008a0   Data        4096  startup_stm32f407xx.o(HEAP)
    STACK                                    0x200018a0   Section     8192  startup_stm32f407xx.o(STACK)
    Stack_Mem                                0x200018a0   Data        8192  startup_stm32f407xx.o(STACK)
    __initial_sp                             0x200038a0   Data           0  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    __user_heap_extent                        - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_free                               - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _scanf_longlong                           - Undefined Weak Reference
    _scanf_mbtowc                             - Undefined Weak Reference
    _scanf_real                               - Undefined Weak Reference
    _scanf_string                             - Undefined Weak Reference
    _scanf_wctomb                             - Undefined Weak Reference
    _scanf_wstring                            - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000191   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000191   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x0800019f   Thumb Code     0  __scatter.o(!!!scatter)
    __decompress                             0x080001c5   Thumb Code    90  __dczerorl2.o(!!dczerorl2)
    __decompress1                            0x080001c5   Thumb Code     0  __dczerorl2.o(!!dczerorl2)
    __scatterload_zeroinit                   0x08000221   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_n                                0x0800023d   Thumb Code     0  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    _printf_percent                          0x0800023d   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_p                                0x08000243   Thumb Code     0  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    _printf_f                                0x08000249   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_e                                0x0800024f   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    _printf_g                                0x08000255   Thumb Code     0  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    _printf_a                                0x0800025b   Thumb Code     0  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    _printf_ll                               0x08000261   Thumb Code     0  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    _printf_i                                0x0800026b   Thumb Code     0  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    _printf_d                                0x08000271   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x08000277   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_o                                0x0800027d   Thumb Code     0  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    _printf_x                                0x08000283   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_lli                              0x08000289   Thumb Code     0  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    _printf_lld                              0x0800028f   Thumb Code     0  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    _printf_llu                              0x08000295   Thumb Code     0  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    _printf_llo                              0x0800029b   Thumb Code     0  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    _printf_llx                              0x080002a1   Thumb Code     0  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    _printf_l                                0x080002a7   Thumb Code     0  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    _printf_c                                0x080002b1   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x080002b7   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_lc                               0x080002bd   Thumb Code     0  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    _printf_ls                               0x080002c3   Thumb Code     0  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    _printf_percent_end                      0x080002c9   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x080002cd   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x080002cf   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000001)
    __rt_lib_init_heap_2                     0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000005)
    __rt_lib_init_preinit_1                  0x080002d3   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_heap_1                     0x080002db   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x080002db   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_rand_1                     0x080002db   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x080002db   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x080002e1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x080002e1   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_lc_ctype_1                 0x080002ed   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x080002ed   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x080002ed   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x080002f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x080002f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x080002f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x080002f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x080002f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x080002f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x080002f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x080002f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x080002f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x080002f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x080002f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x080002f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x080002f7   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x080002f9   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x080002fb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x080002fb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x080002fb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x080002fb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x080002fb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x080002fb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x080002fb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x080002fb   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x080002fd   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x080002fd   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x080002fd   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x08000303   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x08000303   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000307   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000307   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800030f   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x08000311   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x08000311   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000315   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    Reset_Handler                            0x0800031d   Thumb Code     8  startup_stm32f407xx.o(.text)
    _maybe_terminate_alloc                   0x0800031d   Thumb Code     0  maybetermalloc1.o(.emb_text)
    ADC_IRQHandler                           0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART4_IRQHandler                         0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART2_IRQHandler                        0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART3_IRQHandler                        0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART6_IRQHandler                        0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x08000337   Thumb Code     0  startup_stm32f407xx.o(.text)
    __user_initial_stackheap                 0x08000339   Thumb Code     0  startup_stm32f407xx.o(.text)
    malloc                                   0x0800035d   Thumb Code    94  h1_alloc.o(.text)
    free                                     0x080003bb   Thumb Code    78  h1_free.o(.text)
    __aeabi_uldivmod                         0x08000409   Thumb Code     0  lludivv7m.o(.text)
    _ll_udiv                                 0x08000409   Thumb Code   238  lludivv7m.o(.text)
    vsnprintf                                0x080004f9   Thumb Code    48  vsnprintf.o(.text)
    __0sscanf                                0x0800052d   Thumb Code    52  __0sscanf.o(.text)
    _scanf_int                               0x08000569   Thumb Code   332  _scanf_int.o(.text)
    __aeabi_assert                           0x080006b5   Thumb Code    86  assert.o(.text)
    __assert                                 0x080006b5   Thumb Code     0  assert.o(.text)
    __aeabi_memcpy                           0x08000735   Thumb Code     0  rt_memcpy_v6.o(.text)
    __rt_memcpy                              0x08000735   Thumb Code   138  rt_memcpy_v6.o(.text)
    _memcpy_lastbytes                        0x0800079b   Thumb Code     0  rt_memcpy_v6.o(.text)
    __aeabi_memclr4                          0x080007bf   Thumb Code     0  rt_memclr_w.o(.text)
    __aeabi_memclr8                          0x080007bf   Thumb Code     0  rt_memclr_w.o(.text)
    __rt_memclr_w                            0x080007bf   Thumb Code    78  rt_memclr_w.o(.text)
    _memset_w                                0x080007c3   Thumb Code     0  rt_memclr_w.o(.text)
    __use_two_region_memory                  0x0800080d   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow                         0x0800080f   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand                         0x08000811   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_descriptor                     0x08000815   Thumb Code     8  rt_heap_descriptor_intlibspace.o(.text)
    __use_no_heap                            0x0800081d   Thumb Code     2  hguard.o(.text)
    __heap$guard                             0x0800081f   Thumb Code     2  hguard.o(.text)
    _terminate_user_alloc                    0x08000821   Thumb Code     2  init_alloc.o(.text)
    _init_user_alloc                         0x08000823   Thumb Code     2  init_alloc.o(.text)
    __Heap_Full                              0x08000825   Thumb Code    34  init_alloc.o(.text)
    __Heap_Broken                            0x08000847   Thumb Code     6  init_alloc.o(.text)
    _init_alloc                              0x0800084d   Thumb Code    94  init_alloc.o(.text)
    __Heap_Initialize                        0x080008ab   Thumb Code    10  h1_init.o(.text)
    __Heap_DescSize                          0x080008b5   Thumb Code     4  h1_init.o(.text)
    __read_errno                             0x080008b9   Thumb Code    10  _rserrno.o(.text)
    __set_errno                              0x080008c3   Thumb Code    12  _rserrno.o(.text)
    _printf_pre_padding                      0x080008cf   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x080008fb   Thumb Code    34  _printf_pad.o(.text)
    _printf_truncate_signed                  0x0800091d   Thumb Code    18  _printf_truncate.o(.text)
    _printf_truncate_unsigned                0x0800092f   Thumb Code    18  _printf_truncate.o(.text)
    _printf_str                              0x08000941   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x08000995   Thumb Code   104  _printf_dec.o(.text)
    _printf_charcount                        0x08000a0d   Thumb Code    40  _printf_charcount.o(.text)
    _printf_char_common                      0x08000a3f   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08000a65   Thumb Code    10  _sputc.o(.text)
    _snputc                                  0x08000a6f   Thumb Code    16  _snputc.o(.text)
    _printf_wctomb                           0x08000a81   Thumb Code   182  _printf_wctomb.o(.text)
    _printf_longlong_dec                     0x08000b3d   Thumb Code   108  _printf_longlong_dec.o(.text)
    _printf_longlong_oct                     0x08000bb9   Thumb Code    66  _printf_oct_int_ll.o(.text)
    _printf_int_oct                          0x08000bfb   Thumb Code    24  _printf_oct_int_ll.o(.text)
    _printf_ll_oct                           0x08000c13   Thumb Code    12  _printf_oct_int_ll.o(.text)
    _printf_longlong_hex                     0x08000c29   Thumb Code    86  _printf_hex_int_ll_ptr.o(.text)
    _printf_int_hex                          0x08000c7f   Thumb Code    28  _printf_hex_int_ll_ptr.o(.text)
    _printf_ll_hex                           0x08000c9b   Thumb Code    12  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_ptr                          0x08000ca7   Thumb Code    18  _printf_hex_int_ll_ptr.o(.text)
    __printf                                 0x08000cbd   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    _chval                                   0x08000e45   Thumb Code    28  _chval.o(.text)
    __vfscanf_char                           0x08000e6d   Thumb Code    24  scanf_char.o(.text)
    _sgetc                                   0x08000e8d   Thumb Code    30  _sgetc.o(.text)
    _sbackspace                              0x08000eab   Thumb Code    34  _sgetc.o(.text)
    abort                                    0x08000ecd   Thumb Code    22  abort.o(.text)
    __assert_puts                            0x08000ee3   Thumb Code    20  assert_puts.o(.text)
    __aeabi_memcpy4                          0x08000ef7   Thumb Code     0  rt_memcpy_w.o(.text)
    __aeabi_memcpy8                          0x08000ef7   Thumb Code     0  rt_memcpy_w.o(.text)
    __rt_memcpy_w                            0x08000ef7   Thumb Code   100  rt_memcpy_w.o(.text)
    _memcpy_lastbytes_aligned                0x08000f3f   Thumb Code     0  rt_memcpy_w.o(.text)
    _ttywrch                                 0x08000f5b   Thumb Code    14  sys_wrch.o(.text)
    _sys_exit                                0x08000f69   Thumb Code     8  sys_exit.o(.text)
    __user_libspace                          0x08000f75   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x08000f75   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x08000f75   Thumb Code     0  libspace.o(.text)
    __aeabi_errno_addr                       0x08000f7d   Thumb Code     8  rt_errno_addr_intlibspace.o(.text)
    __errno$intlibspace                      0x08000f7d   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __rt_errno_addr$intlibspace              0x08000f7d   Thumb Code     0  rt_errno_addr_intlibspace.o(.text)
    __Heap_ProvideMemory                     0x08000f85   Thumb Code    52  h1_extend.o(.text)
    _ll_udiv10                               0x08000fb9   Thumb Code   138  lludiv10.o(.text)
    isspace                                  0x08001043   Thumb Code    18  isspace.o(.text)
    _printf_int_common                       0x08001055   Thumb Code   178  _printf_intcommon.o(.text)
    __lib_sel_fp_printf                      0x08001107   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x080012b9   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_fp_hex_real                      0x08001525   Thumb Code   756  _printf_fp_hex.o(.text)
    _printf_cs_common                        0x08001821   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08001835   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08001845   Thumb Code     8  _printf_char.o(.text)
    _printf_lcs_common                       0x0800184d   Thumb Code    20  _printf_wchar.o(.text)
    _printf_wchar                            0x08001861   Thumb Code    16  _printf_wchar.o(.text)
    _printf_wstring                          0x08001871   Thumb Code     8  _printf_wchar.o(.text)
    __vfscanf                                0x08001879   Thumb Code   880  _scanf.o(.text)
    _wcrtomb                                 0x08001bed   Thumb Code    64  _wcrtomb.o(.text)
    __rt_SIGABRT                             0x08001c2d   Thumb Code    14  defsig_abrt_outer.o(.text)
    __rt_SIGRTMEM                            0x08001c3b   Thumb Code    14  defsig_rtmem_outer.o(.text)
    __I$use$semihosting                      0x08001c49   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08001c49   Thumb Code     2  use_no_semi.o(.text)
    __semihosting_library_function           0x08001c4b   Thumb Code     0  indicate_semi.o(.text)
    __user_setup_stackheap                   0x08001c4b   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_ctype_table                         0x08001c95   Thumb Code    16  rt_ctype_table.o(.text)
    __rt_locale                              0x08001ca5   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _printf_fp_infnan                        0x08001cad   Thumb Code   112  _printf_fp_infnan.o(.text)
    _btod_etento                             0x08001d2d   Thumb Code   224  bigflt0.o(.text)
    exit                                     0x08001e11   Thumb Code    18  exit.o(.text)
    __sig_exit                               0x08001e23   Thumb Code    10  defsig_exit.o(.text)
    __rt_SIGABRT_inner                       0x08001e2d   Thumb Code    14  defsig_abrt_inner.o(.text)
    __rt_SIGRTMEM_inner                      0x08001e5d   Thumb Code    22  defsig_rtmem_inner.o(.text)
    __default_signal_display                 0x08001ead   Thumb Code    50  defsig_general.o(.text)
    strcmp                                   0x08001ee1   Thumb Code   128  strcmpv7m.o(.text)
    _btod_d2e                                0x08001f61   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x08001f9f   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08001fe5   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08002045   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x0800237d   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08002459   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x08002483   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x080024ad   Thumb Code   580  btod.o(CL$$btod_mult_common)
    Angle_PID_control                        0x080026f1   Thumb Code   126  pid_app.o(i.Angle_PID_control)
    BNO080_HardwareReset                     0x08002785   Thumb Code   114  bno08x_hal.o(i.BNO080_HardwareReset)
    BNO080_Init                              0x08002801   Thumb Code    10  bno08x_hal.o(i.BNO080_Init)
    BusFault_Handler                         0x08002815   Thumb Code     4  stm32f4xx_it.o(i.BusFault_Handler)
    Car_State_Update                         0x08002819   Thumb Code   326  my_timer.o(i.Car_State_Update)
    DMA1_Stream0_IRQHandler                  0x08002991   Thumb Code    10  stm32f4xx_it.o(i.DMA1_Stream0_IRQHandler)
    DMA2_Stream2_IRQHandler                  0x080029a1   Thumb Code    10  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    DebugMon_Handler                         0x08002b9b   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Delay_us                                 0x08002b9d   Thumb Code    84  software_iic.o(i.Delay_us)
    EXTI3_IRQHandler                         0x08002bf5   Thumb Code    10  stm32f4xx_it.o(i.EXTI3_IRQHandler)
    Encoder_Driver_Init                      0x08002c01   Thumb Code    42  encoder_driver.o(i.Encoder_Driver_Init)
    Encoder_Driver_Update                    0x08002c31   Thumb Code   126  encoder_driver.o(i.Encoder_Driver_Update)
    Encoder_Init                             0x08002cc1   Thumb Code    24  encoder_app.o(i.Encoder_Init)
    Encoder_Task                             0x08002ce9   Thumb Code    16  encoder_app.o(i.Encoder_Task)
    Error_Handler                            0x08002d01   Thumb Code     6  main.o(i.Error_Handler)
    Gray_Init                                0x08002d31   Thumb Code    28  gray_app.o(i.Gray_Init)
    Gray_Task                                0x08002d9d   Thumb Code   108  gray_app.o(i.Gray_Task)
    HAL_DMA_Abort                            0x08002e19   Thumb Code   172  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x08002ec5   Thumb Code    40  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08002eed   Thumb Code   570  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x0800312d   Thumb Code   232  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x08003219   Thumb Code   146  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_Delay                                0x080032ad   Thumb Code    36  stm32f4xx_hal.o(i.HAL_Delay)
    HAL_GPIO_EXTI_Callback                   0x080032d5   Thumb Code     2  stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback)
    HAL_GPIO_EXTI_IRQHandler                 0x080032d9   Thumb Code    24  stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler)
    HAL_GPIO_Init                            0x080032f5   Thumb Code   454  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GPIO_ReadPin                         0x080034e9   Thumb Code    16  stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin)
    HAL_GPIO_WritePin                        0x080034f9   Thumb Code    12  stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin)
    HAL_GetTick                              0x08003505   Thumb Code     6  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_I2C_Init                             0x08003511   Thumb Code   446  stm32f4xx_hal_i2c.o(i.HAL_I2C_Init)
    HAL_I2C_Master_Receive                   0x080036e1   Thumb Code   766  stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Receive)
    HAL_I2C_Master_Transmit                  0x080039ed   Thumb Code   360  stm32f4xx_hal_i2c.o(i.HAL_I2C_Master_Transmit)
    HAL_I2C_Mem_Write                        0x08003b5d   Thumb Code   348  stm32f4xx_hal_i2c.o(i.HAL_I2C_Mem_Write)
    HAL_I2C_MspInit                          0x08003cc1   Thumb Code   228  i2c.o(i.HAL_I2C_MspInit)
    HAL_IncTick                              0x08003db5   Thumb Code    16  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08003dcd   Thumb Code    54  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08003e09   Thumb Code    64  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08003e55   Thumb Code    74  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08003ea5   Thumb Code    40  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08003ecd   Thumb Code   124  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08003f49   Thumb Code    32  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08003f71   Thumb Code   368  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetHCLKFreq                      0x080040f5   Thumb Code     6  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK1Freq                     0x08004101   Thumb Code    22  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08004121   Thumb Code    22  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08004141   Thumb Code   162  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x080041f1   Thumb Code  1172  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x0800468d   Thumb Code    52  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_TIMEx_BreakCallback                  0x080046c1   Thumb Code     2  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_BreakCallback)
    HAL_TIMEx_CommutCallback                 0x080046c3   Thumb Code     2  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_CommutCallback)
    HAL_TIMEx_ConfigBreakDeadTime            0x080046c5   Thumb Code   116  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_ConfigBreakDeadTime)
    HAL_TIMEx_MasterConfigSynchronization    0x08004739   Thumb Code   150  stm32f4xx_hal_tim_ex.o(i.HAL_TIMEx_MasterConfigSynchronization)
    HAL_TIM_Base_Init                        0x080047ed   Thumb Code   102  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Init)
    HAL_TIM_Base_MspInit                     0x08004855   Thumb Code   106  tim.o(i.HAL_TIM_Base_MspInit)
    HAL_TIM_Base_Start_IT                    0x080048c9   Thumb Code   138  stm32f4xx_hal_tim.o(i.HAL_TIM_Base_Start_IT)
    HAL_TIM_ConfigClockSource                0x08004971   Thumb Code   268  stm32f4xx_hal_tim.o(i.HAL_TIM_ConfigClockSource)
    HAL_TIM_Encoder_Init                     0x08004a7d   Thumb Code   200  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Init)
    HAL_TIM_Encoder_MspInit                  0x08004b45   Thumb Code   226  tim.o(i.HAL_TIM_Encoder_MspInit)
    HAL_TIM_Encoder_Start                    0x08004c3d   Thumb Code   204  stm32f4xx_hal_tim.o(i.HAL_TIM_Encoder_Start)
    HAL_TIM_IC_CaptureCallback               0x08004d09   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_IC_CaptureCallback)
    HAL_TIM_IRQHandler                       0x08004d0b   Thumb Code   364  stm32f4xx_hal_tim.o(i.HAL_TIM_IRQHandler)
    HAL_TIM_MspPostInit                      0x08004e79   Thumb Code    86  tim.o(i.HAL_TIM_MspPostInit)
    HAL_TIM_OC_DelayElapsedCallback          0x08004edd   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_OC_DelayElapsedCallback)
    HAL_TIM_PWM_ConfigChannel                0x08004edf   Thumb Code   260  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_ConfigChannel)
    HAL_TIM_PWM_Init                         0x08004fe3   Thumb Code   102  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Init)
    HAL_TIM_PWM_MspInit                      0x08005049   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_MspInit)
    HAL_TIM_PWM_PulseFinishedCallback        0x0800504b   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_PulseFinishedCallback)
    HAL_TIM_PWM_Start                        0x0800504d   Thumb Code   238  stm32f4xx_hal_tim.o(i.HAL_TIM_PWM_Start)
    HAL_TIM_PeriodElapsedCallback            0x08005159   Thumb Code   274  my_timer.o(i.HAL_TIM_PeriodElapsedCallback)
    HAL_TIM_TriggerCallback                  0x0800529d   Thumb Code     2  stm32f4xx_hal_tim.o(i.HAL_TIM_TriggerCallback)
    HAL_UARTEx_ReceiveToIdle_DMA             0x0800529f   Thumb Code   112  stm32f4xx_hal_uart.o(i.HAL_UARTEx_ReceiveToIdle_DMA)
    HAL_UARTEx_RxEventCallback               0x08005311   Thumb Code    66  usart_app.o(i.HAL_UARTEx_RxEventCallback)
    HAL_UART_DMAStop                         0x08005369   Thumb Code   138  stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop)
    HAL_UART_ErrorCallback                   0x080053f3   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x080053f5   Thumb Code   772  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x080056fd   Thumb Code   118  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08005775   Thumb Code   440  usart.o(i.HAL_UART_MspInit)
    HAL_UART_RxCpltCallback                  0x08005955   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x08005957   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit                        0x08005959   Thumb Code   190  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit)
    HAL_UART_TxCpltCallback                  0x08005a17   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HardFault_Handler                        0x08005a19   Thumb Code     4  stm32f4xx_it.o(i.HardFault_Handler)
    IIC_Get_Digtal                           0x0800604d   Thumb Code    32  software_iic.o(i.IIC_Get_Digtal)
    IIC_ReadBytes                            0x0800606d   Thumb Code   118  software_iic.o(i.IIC_ReadBytes)
    IIC_RecvByte                             0x080060e5   Thumb Code   120  software_iic.o(i.IIC_RecvByte)
    IIC_SendAck                              0x08006161   Thumb Code    48  software_iic.o(i.IIC_SendAck)
    IIC_SendByte                             0x08006195   Thumb Code    82  software_iic.o(i.IIC_SendByte)
    IIC_SendNAck                             0x080061ed   Thumb Code    38  software_iic.o(i.IIC_SendNAck)
    IIC_Start                                0x08006219   Thumb Code    52  software_iic.o(i.IIC_Start)
    IIC_Stop                                 0x08006251   Thumb Code    46  software_iic.o(i.IIC_Stop)
    IIC_WaitAck                              0x08006285   Thumb Code    54  software_iic.o(i.IIC_WaitAck)
    Line_PID_control                         0x080062c1   Thumb Code   112  pid_app.o(i.Line_PID_control)
    MX_DMA_Init                              0x08006349   Thumb Code    98  dma.o(i.MX_DMA_Init)
    MX_GPIO_Init                             0x080063b1   Thumb Code   440  gpio.o(i.MX_GPIO_Init)
    MX_I2C1_Init                             0x0800657d   Thumb Code    48  i2c.o(i.MX_I2C1_Init)
    MX_I2C2_Init                             0x080065b9   Thumb Code    48  i2c.o(i.MX_I2C2_Init)
    MX_TIM1_Init                             0x080065f5   Thumb Code   254  tim.o(i.MX_TIM1_Init)
    MX_TIM2_Init                             0x080066fd   Thumb Code   102  tim.o(i.MX_TIM2_Init)
    MX_TIM3_Init                             0x08006769   Thumb Code   120  tim.o(i.MX_TIM3_Init)
    MX_TIM4_Init                             0x080067e9   Thumb Code   120  tim.o(i.MX_TIM4_Init)
    MX_UART5_Init                            0x08006869   Thumb Code    46  usart.o(i.MX_UART5_Init)
    MX_USART1_UART_Init                      0x080068a1   Thumb Code    46  usart.o(i.MX_USART1_UART_Init)
    MemManage_Handler                        0x080068d9   Thumb Code     4  stm32f4xx_it.o(i.MemManage_Handler)
    Motor_Create                             0x080068dd   Thumb Code   190  motor_driver.o(i.Motor_Create)
    Motor_Init                               0x080069a1   Thumb Code    80  motor_app.o(i.Motor_Init)
    Motor_SetSpeed                           0x08006a01   Thumb Code   160  motor_driver.o(i.Motor_SetSpeed)
    NMI_Handler                              0x08006aeb   Thumb Code     4  stm32f4xx_it.o(i.NMI_Handler)
    OLED_Clear                               0x08006aef   Thumb Code    56  oled.o(i.OLED_Clear)
    OLED_Init                                0x08006b29   Thumb Code    42  oled.o(i.OLED_Init)
    OLED_Set_Position                        0x08006b59   Thumb Code    36  oled.o(i.OLED_Set_Position)
    OLED_ShowChar                            0x08006b7d   Thumb Code   148  oled.o(i.OLED_ShowChar)
    OLED_ShowStr                             0x08006c19   Thumb Code    58  oled.o(i.OLED_ShowStr)
    OLED_Write_cmd                           0x08006c55   Thumb Code    32  oled.o(i.OLED_Write_cmd)
    OLED_Write_data                          0x08006c79   Thumb Code    32  oled.o(i.OLED_Write_data)
    PID_Init                                 0x08006c9d   Thumb Code   232  pid_app.o(i.PID_Init)
    PID_Task                                 0x08006dad   Thumb Code   272  pid_app.o(i.PID_Task)
    PendSV_Handler                           0x08006f01   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    Ping                                     0x08006f03   Thumb Code    30  software_iic.o(i.Ping)
    QuaternionToEulerAngles                  0x08006f21   Thumb Code   472  bno08x_hal.o(i.QuaternionToEulerAngles)
    SVC_Handler                              0x08007115   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x080071d5   Thumb Code     8  stm32f4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x080071dd   Thumb Code   170  main.o(i.SystemClock_Config)
    SystemInit                               0x08007291   Thumb Code    14  system_stm32f4xx.o(i.SystemInit)
    TIM2_IRQHandler                          0x080072a5   Thumb Code    10  stm32f4xx_it.o(i.TIM2_IRQHandler)
    TIM_Base_SetConfig                       0x080072b5   Thumb Code   178  stm32f4xx_hal_tim.o(i.TIM_Base_SetConfig)
    TIM_CCxChannelCmd                        0x08007395   Thumb Code    34  stm32f4xx_hal_tim.o(i.TIM_CCxChannelCmd)
    TIM_ETR_SetConfig                        0x080073b7   Thumb Code    22  stm32f4xx_hal_tim.o(i.TIM_ETR_SetConfig)
    TIM_OC2_SetConfig                        0x08007451   Thumb Code   114  stm32f4xx_hal_tim.o(i.TIM_OC2_SetConfig)
    UART5_IRQHandler                         0x080075e9   Thumb Code    10  stm32f4xx_it.o(i.UART5_IRQHandler)
    UART_Start_Receive_DMA                   0x08007b0d   Thumb Code   202  stm32f4xx_hal_uart.o(i.UART_Start_Receive_DMA)
    USART1_IRQHandler                        0x08007cd1   Thumb Code    10  stm32f4xx_it.o(i.USART1_IRQHandler)
    UsageFault_Handler                       0x08007ce1   Thumb Code     4  stm32f4xx_it.o(i.UsageFault_Handler)
    __ARM_fpclassify                         0x08007ce5   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __ARM_fpclassifyf                        0x08007d15   Thumb Code    38  fpclassifyf.o(i.__ARM_fpclassifyf)
    __hardfp_asinf                           0x08007d75   Thumb Code   258  asinf.o(i.__hardfp_asinf)
    __hardfp_atan2f                          0x08007ea1   Thumb Code   594  atan2f.o(i.__hardfp_atan2f)
    __hardfp_powf                            0x0800814d   Thumb Code  1606  powf.o(i.__hardfp_powf)
    __mathlib_powf                           0x0800814d   Thumb Code     0  powf.o(i.__hardfp_powf)
    __hardfp_roundf                          0x080087b1   Thumb Code   154  roundf.o(i.__hardfp_roundf)
    __hardfp_sqrtf                           0x0800884b   Thumb Code    58  sqrtf.o(i.__hardfp_sqrtf)
    __mathlib_flt_divzero                    0x08008885   Thumb Code    14  funder.o(i.__mathlib_flt_divzero)
    __mathlib_flt_infnan                     0x08008899   Thumb Code     6  funder.o(i.__mathlib_flt_infnan)
    __mathlib_flt_infnan2                    0x0800889f   Thumb Code     6  funder.o(i.__mathlib_flt_infnan2)
    __mathlib_flt_invalid                    0x080088a5   Thumb Code    10  funder.o(i.__mathlib_flt_invalid)
    __mathlib_flt_overflow                   0x080088b5   Thumb Code    10  funder.o(i.__mathlib_flt_overflow)
    __mathlib_flt_underflow                  0x080088c5   Thumb Code    10  funder.o(i.__mathlib_flt_underflow)
    _is_digit                                0x080088d5   Thumb Code    14  __printf_wp.o(i._is_digit)
    all_task_init                            0x080088e5   Thumb Code    46  my_scheduler.o(i.all_task_init)
    all_task_run                             0x08008919   Thumb Code    78  my_scheduler.o(i.all_task_run)
    bno080_task                              0x08008971   Thumb Code   124  bno08x_app.o(i.bno080_task)
    convert_to_continuous_yaw                0x08008a01   Thumb Code   126  bno08x_app.o(i.convert_to_continuous_yaw)
    dataAvailable                            0x08008a99   Thumb Code    38  bno08x_hal.o(i.dataAvailable)
    enableRotationVector                     0x08008ac9   Thumb Code    16  bno08x_hal.o(i.enableRotationVector)
    getQuatI                                 0x08008ad9   Thumb Code    18  bno08x_hal.o(i.getQuatI)
    getQuatJ                                 0x08008af5   Thumb Code    18  bno08x_hal.o(i.getQuatJ)
    getQuatK                                 0x08008b11   Thumb Code    18  bno08x_hal.o(i.getQuatK)
    getQuatReal                              0x08008b2d   Thumb Code    18  bno08x_hal.o(i.getQuatReal)
    get_yaw                                  0x08008b49   Thumb Code    22  bno08x_app.o(i.get_yaw)
    key_read                                 0x08008b65   Thumb Code    68  key_driver.o(i.key_read)
    key_task                                 0x08008bb1   Thumb Code   120  key_app.o(i.key_task)
    led_disp                                 0x08008c3d   Thumb Code   120  led_driver.o(i.led_disp)
    led_init                                 0x08008cc1   Thumb Code     2  led_app.o(i.led_init)
    led_task                                 0x08008cc5   Thumb Code    10  led_app.o(i.led_task)
    main                                     0x08008cd5   Thumb Code    60  main.o(i.main)
    motor_set_l                              0x08008d11   Thumb Code    26  motor_app.o(i.motor_set_l)
    motor_set_r                              0x08008d31   Thumb Code    26  motor_app.o(i.motor_set_r)
    my_bno080_init                           0x08008d51   Thumb Code   100  bno08x_app.o(i.my_bno080_init)
    my_oled_init                             0x08008e75   Thumb Code     8  oled_app.o(i.my_oled_init)
    my_printf                                0x08008e7d   Thumb Code    58  usart_app.o(i.my_printf)
    oled_printf                              0x08008eb7   Thumb Code    60  oled_app.o(i.oled_printf)
    oled_task                                0x08008ef5   Thumb Code   148  oled_app.o(i.oled_task)
    parseInputReport                         0x08008fb9   Thumb Code   366  bno08x_hal.o(i.parseInputReport)
    pid_calculate_positional                 0x08009199   Thumb Code    38  pid.o(i.pid_calculate_positional)
    pid_constrain                            0x080091bf   Thumb Code    42  pid.o(i.pid_constrain)
    pid_init                                 0x08009265   Thumb Code    78  pid.o(i.pid_init)
    pid_reset                                0x080092f9   Thumb Code    58  pid.o(i.pid_reset)
    pid_set_target                           0x08009339   Thumb Code     6  pid.o(i.pid_set_target)
    qToFloat                                 0x0800933f   Thumb Code    58  bno08x_hal.o(i.qToFloat)
    rt_ringbuffer_data_len                   0x0800943d   Thumb Code    60  ringbuffer.o(i.rt_ringbuffer_data_len)
    rt_ringbuffer_get                        0x08009479   Thumb Code   180  ringbuffer.o(i.rt_ringbuffer_get)
    rt_ringbuffer_init                       0x08009565   Thumb Code    84  ringbuffer.o(i.rt_ringbuffer_init)
    rt_ringbuffer_put                        0x080095f9   Thumb Code   182  ringbuffer.o(i.rt_ringbuffer_put)
    setFeatureCommand                        0x08009795   Thumb Code    86  bno08x_hal.o(i.setFeatureCommand)
    softReset                                0x080097f1   Thumb Code    50  bno08x_hal.o(i.softReset)
    sqrtf                                    0x08009829   Thumb Code    62  sqrtf.o(i.sqrtf)
    timer_init                               0x08009869   Thumb Code    10  my_timer.o(i.timer_init)
    uart_init                                0x08009879   Thumb Code    40  usart_app.o(i.uart_init)
    uart_task                                0x080098b5   Thumb Code   168  usart_app.o(i.uart_task)
    _get_lc_numeric                          0x080099ad   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x080099d9   Thumb Code    44  lc_ctype_c.o(locale$$code)
    __aeabi_d2f                              0x08009a05   Thumb Code     0  d2f.o(x$fpl$d2f)
    _d2f                                     0x08009a05   Thumb Code    98  d2f.o(x$fpl$d2f)
    __aeabi_ddiv                             0x08009a69   Thumb Code     0  ddiv.o(x$fpl$ddiv)
    _ddiv                                    0x08009a69   Thumb Code   552  ddiv.o(x$fpl$ddiv)
    __aeabi_i2d                              0x08009d19   Thumb Code     0  dflt_clz.o(x$fpl$dflt)
    _dflt                                    0x08009d19   Thumb Code    46  dflt_clz.o(x$fpl$dflt)
    __aeabi_dmul                             0x08009d49   Thumb Code     0  dmul.o(x$fpl$dmul)
    _dmul                                    0x08009d49   Thumb Code   332  dmul.o(x$fpl$dmul)
    __fpl_dnaninf                            0x08009e9d   Thumb Code   156  dnaninf.o(x$fpl$dnaninf)
    __fpl_dretinf                            0x08009f39   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_f2d                              0x08009f45   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x08009f45   Thumb Code    86  f2d.o(x$fpl$f2d)
    __fpl_fnaninf                            0x08009f9b   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    _fp_init                                 0x0800a027   Thumb Code    10  fpinit.o(x$fpl$fpinit)
    __fplib_config_fpu_vfp                   0x0800a02f   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fplib_config_pureend_doubles           0x0800a02f   Thumb Code     0  fpinit.o(x$fpl$fpinit)
    __fpl_fretinf                            0x0800a031   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    _frnd                                    0x0800a03d   Thumb Code    96  frnd.o(x$fpl$frnd)
    _printf_fp_dec                           0x0800a09d   Thumb Code     4  printf1.o(x$fpl$printf1)
    _printf_fp_hex                           0x0800a0a1   Thumb Code     4  printf2.o(x$fpl$printf2)
    __I$use$fp                               0x0800a0a4   Number         0  usenofp.o(x$fpl$usenofp)
    AHBPrescTable                            0x0800a0ac   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x0800a0bc   Data           8  system_stm32f4xx.o(.constdata)
    F6X8                                     0x0800a0c4   Data         552  oled.o(.constdata)
    Hzk                                      0x0800a8dc   Data         128  oled.o(.constdata)
    Hzb                                      0x0800a95c   Data         512  oled.o(.constdata)
    Region$$Table$$Base                      0x0800ad98   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800adb8   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x0800ade1   Data           0  lc_ctype_c.o(locale$$data)
    uwTick                                   0x20000000   Data           4  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x20000004   Data           4  stm32f4xx_hal.o(.data)
    uwTickFreq                               0x20000008   Data           1  stm32f4xx_hal.o(.data)
    SystemCoreClock                          0x2000000c   Data           4  system_stm32f4xx.o(.data)
    initcmd1                                 0x20000010   Data          22  oled.o(.data)
    led_rgb                                  0x20000081   Data           5  led_app.o(.data)
    runing_flat                              0x20000086   Data           1  key_app.o(.data)
    runing_time                              0x20000088   Data           2  key_app.o(.data)
    test                                     0x2000008a   Data           1  key_app.o(.data)
    basic_speed                              0x2000008c   Data           4  pid_app.o(.data)
    pid_params_left                          0x20000090   Data          20  pid_app.o(.data)
    pid_params_right                         0x200000a4   Data          20  pid_app.o(.data)
    pid_params_line                          0x200000b8   Data          20  pid_app.o(.data)
    pid_params_angle                         0x200000cc   Data          20  pid_app.o(.data)
    pid_running                              0x200000e0   Data           1  pid_app.o(.data)
    pid_control_mode                         0x200000e1   Data           1  pid_app.o(.data)
    stop_flat                                0x200000e2   Data           1  pid_app.o(.data)
    angle_flat                               0x200000e3   Data           1  pid_app.o(.data)
    Digtal                                   0x200000e4   Data           1  gray_app.o(.data)
    gray_weights                             0x200000e8   Data          32  gray_app.o(.data)
    g_line_position_error                    0x20000108   Data           4  gray_app.o(.data)
    roll                                     0x2000010c   Data           4  bno08x_app.o(.data)
    pitch                                    0x20000110   Data           4  bno08x_app.o(.data)
    yaw                                      0x20000114   Data           4  bno08x_app.o(.data)
    first_flat                               0x20000118   Data           1  bno08x_app.o(.data)
    frist_yaw                                0x2000011c   Data           4  bno08x_app.o(.data)
    g_last_yaw                               0x20000120   Data           4  bno08x_app.o(.data)
    g_revolution_count                       0x20000124   Data           4  bno08x_app.o(.data)
    g_is_yaw_initialized                     0x20000128   Data           1  bno08x_app.o(.data)
    all_task                                 0x2000012c   Data          48  my_scheduler.o(.data)
    task_num                                 0x2000015c   Data           1  my_scheduler.o(.data)
    measure_timer5ms                         0x20000160   Data           1  my_timer.o(.data)
    key_timer10ms                            0x20000161   Data           1  my_timer.o(.data)
    output_ff_flag                           0x20000162   Data           1  my_timer.o(.data)
    output_timer500ms                        0x20000164   Data           4  my_timer.o(.data)
    intput_ff_flag                           0x20000168   Data           1  my_timer.o(.data)
    intput_timer500ms                        0x2000016c   Data           4  my_timer.o(.data)
    led_timer500ms                           0x20000170   Data           4  my_timer.o(.data)
    point_count                              0x20000174   Data           1  my_timer.o(.data)
    system_mode                              0x20000175   Data           1  my_timer.o(.data)
    circle_count                             0x20000176   Data           1  my_timer.o(.data)
    distance                                 0x20000178   Data           4  my_timer.o(.data)
    hi2c1                                    0x2000017c   Data          84  i2c.o(.bss)
    hi2c2                                    0x200001d0   Data          84  i2c.o(.bss)
    htim1                                    0x20000224   Data          72  tim.o(.bss)
    htim2                                    0x2000026c   Data          72  tim.o(.bss)
    htim3                                    0x200002b4   Data          72  tim.o(.bss)
    htim4                                    0x200002fc   Data          72  tim.o(.bss)
    huart5                                   0x20000344   Data          72  usart.o(.bss)
    huart1                                   0x2000038c   Data          72  usart.o(.bss)
    hdma_uart5_rx                            0x200003d4   Data          96  usart.o(.bss)
    hdma_usart1_rx                           0x20000434   Data          96  usart.o(.bss)
    uart_rx_dma_buffer                       0x20000544   Data         128  usart_app.o(.bss)
    uart_dma_buffer                          0x200005c4   Data         128  usart_app.o(.bss)
    uart_ringbuffer                          0x20000644   Data          12  usart_app.o(.bss)
    ringbuffer_pool                          0x20000650   Data         128  usart_app.o(.bss)
    right_motor                              0x200006d0   Data          48  motor_app.o(.bss)
    left_motor                               0x20000700   Data          48  motor_app.o(.bss)
    left_encoder                             0x20000730   Data          16  encoder_app.o(.bss)
    right_encoder                            0x20000740   Data          16  encoder_app.o(.bss)
    pid_speed_left                           0x20000750   Data          60  pid_app.o(.bss)
    pid_speed_right                          0x2000078c   Data          60  pid_app.o(.bss)
    pid_line                                 0x200007c8   Data          60  pid_app.o(.bss)
    pid_angle                                0x20000804   Data          60  pid_app.o(.bss)
    __libspace_start                         0x20000840   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x200008a0   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000b060, Max: 0x00080000, ABSOLUTE, COMPRESSED[0x0000af80])

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000aee4, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000008   Code   RO         5785  * !!!main             c_w.l(__main.o)
    0x08000190   0x08000190   0x00000034   Code   RO         6289    !!!scatter          c_w.l(__scatter.o)
    0x080001c4   0x080001c4   0x0000005a   Code   RO         6287    !!dczerorl2         c_w.l(__dczerorl2.o)
    0x0800021e   0x0800021e   0x00000002   PAD
    0x08000220   0x08000220   0x0000001c   Code   RO         6291    !!handler_zi        c_w.l(__scatter_zi.o)
    0x0800023c   0x0800023c   0x00000000   Code   RO         5974    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x0800023c   0x0800023c   0x00000006   Code   RO         5963    .ARM.Collect$$_printf_percent$$00000001  c_w.l(_printf_n.o)
    0x08000242   0x08000242   0x00000006   Code   RO         5965    .ARM.Collect$$_printf_percent$$00000002  c_w.l(_printf_p.o)
    0x08000248   0x08000248   0x00000006   Code   RO         5970    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x0800024e   0x0800024e   0x00000006   Code   RO         5971    .ARM.Collect$$_printf_percent$$00000004  c_w.l(_printf_e.o)
    0x08000254   0x08000254   0x00000006   Code   RO         5972    .ARM.Collect$$_printf_percent$$00000005  c_w.l(_printf_g.o)
    0x0800025a   0x0800025a   0x00000006   Code   RO         5973    .ARM.Collect$$_printf_percent$$00000006  c_w.l(_printf_a.o)
    0x08000260   0x08000260   0x0000000a   Code   RO         5978    .ARM.Collect$$_printf_percent$$00000007  c_w.l(_printf_ll.o)
    0x0800026a   0x0800026a   0x00000006   Code   RO         5967    .ARM.Collect$$_printf_percent$$00000008  c_w.l(_printf_i.o)
    0x08000270   0x08000270   0x00000006   Code   RO         5968    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x08000276   0x08000276   0x00000006   Code   RO         5969    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x0800027c   0x0800027c   0x00000006   Code   RO         5966    .ARM.Collect$$_printf_percent$$0000000B  c_w.l(_printf_o.o)
    0x08000282   0x08000282   0x00000006   Code   RO         5964    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x08000288   0x08000288   0x00000006   Code   RO         5975    .ARM.Collect$$_printf_percent$$0000000D  c_w.l(_printf_lli.o)
    0x0800028e   0x0800028e   0x00000006   Code   RO         5976    .ARM.Collect$$_printf_percent$$0000000E  c_w.l(_printf_lld.o)
    0x08000294   0x08000294   0x00000006   Code   RO         5977    .ARM.Collect$$_printf_percent$$0000000F  c_w.l(_printf_llu.o)
    0x0800029a   0x0800029a   0x00000006   Code   RO         5982    .ARM.Collect$$_printf_percent$$00000010  c_w.l(_printf_llo.o)
    0x080002a0   0x080002a0   0x00000006   Code   RO         5983    .ARM.Collect$$_printf_percent$$00000011  c_w.l(_printf_llx.o)
    0x080002a6   0x080002a6   0x0000000a   Code   RO         5979    .ARM.Collect$$_printf_percent$$00000012  c_w.l(_printf_l.o)
    0x080002b0   0x080002b0   0x00000006   Code   RO         5961    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x080002b6   0x080002b6   0x00000006   Code   RO         5962    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x080002bc   0x080002bc   0x00000006   Code   RO         5980    .ARM.Collect$$_printf_percent$$00000015  c_w.l(_printf_lc.o)
    0x080002c2   0x080002c2   0x00000006   Code   RO         5981    .ARM.Collect$$_printf_percent$$00000016  c_w.l(_printf_ls.o)
    0x080002c8   0x080002c8   0x00000004   Code   RO         6136    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x080002cc   0x080002cc   0x00000002   Code   RO         6223    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x080002ce   0x080002ce   0x00000004   Code   RO         5998    .ARM.Collect$$libinit$$00000001  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000000   Code   RO         6001    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x080002d2   0x080002d2   0x00000008   Code   RO         6002    .ARM.Collect$$libinit$$00000005  c_w.l(libinit2.o)
    0x080002da   0x080002da   0x00000000   Code   RO         6004    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x080002da   0x080002da   0x00000000   Code   RO         6006    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x080002da   0x080002da   0x00000000   Code   RO         6008    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x080002da   0x080002da   0x00000006   Code   RO         6009    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x080002e0   0x080002e0   0x00000000   Code   RO         6011    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x080002e0   0x080002e0   0x0000000c   Code   RO         6012    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x080002ec   0x080002ec   0x00000000   Code   RO         6013    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x080002ec   0x080002ec   0x00000000   Code   RO         6015    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x080002ec   0x080002ec   0x0000000a   Code   RO         6016    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x080002f6   0x080002f6   0x00000000   Code   RO         6017    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x080002f6   0x080002f6   0x00000000   Code   RO         6019    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x080002f6   0x080002f6   0x00000000   Code   RO         6021    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x080002f6   0x080002f6   0x00000000   Code   RO         6023    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x080002f6   0x080002f6   0x00000000   Code   RO         6025    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x080002f6   0x080002f6   0x00000000   Code   RO         6027    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x080002f6   0x080002f6   0x00000000   Code   RO         6029    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x080002f6   0x080002f6   0x00000000   Code   RO         6031    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x080002f6   0x080002f6   0x00000000   Code   RO         6035    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x080002f6   0x080002f6   0x00000000   Code   RO         6037    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x080002f6   0x080002f6   0x00000000   Code   RO         6039    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x080002f6   0x080002f6   0x00000000   Code   RO         6041    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x080002f6   0x080002f6   0x00000002   Code   RO         6042    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x080002f8   0x080002f8   0x00000002   Code   RO         6284    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x080002fa   0x080002fa   0x00000000   Code   RO         6244    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x080002fa   0x080002fa   0x00000000   Code   RO         6246    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x080002fa   0x080002fa   0x00000000   Code   RO         6248    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x080002fa   0x080002fa   0x00000000   Code   RO         6251    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x080002fa   0x080002fa   0x00000000   Code   RO         6254    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x080002fa   0x080002fa   0x00000000   Code   RO         6256    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x080002fa   0x080002fa   0x00000000   Code   RO         6259    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x080002fa   0x080002fa   0x00000002   Code   RO         6260    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x080002fc   0x080002fc   0x00000000   Code   RO         5859    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x080002fc   0x080002fc   0x00000000   Code   RO         6090    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x080002fc   0x080002fc   0x00000006   Code   RO         6102    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x08000302   0x08000302   0x00000000   Code   RO         6092    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x08000302   0x08000302   0x00000004   Code   RO         6093    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000306   0x08000306   0x00000000   Code   RO         6095    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000306   0x08000306   0x00000008   Code   RO         6096    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800030e   0x0800030e   0x00000002   Code   RO         6226    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x08000310   0x08000310   0x00000000   Code   RO         6262    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x08000310   0x08000310   0x00000004   Code   RO         6263    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000314   0x08000314   0x00000006   Code   RO         6264    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x0800031a   0x0800031a   0x00000002   PAD
    0x0800031c   0x0800031c   0x00000000   Code   RO         6113    .emb_text           c_w.l(maybetermalloc1.o)
    0x0800031c   0x0800031c   0x00000040   Code   RO            4    .text               startup_stm32f407xx.o
    0x0800035c   0x0800035c   0x0000005e   Code   RO         5711    .text               c_w.l(h1_alloc.o)
    0x080003ba   0x080003ba   0x0000004e   Code   RO         5713    .text               c_w.l(h1_free.o)
    0x08000408   0x08000408   0x000000ee   Code   RO         5769    .text               c_w.l(lludivv7m.o)
    0x080004f6   0x080004f6   0x00000002   PAD
    0x080004f8   0x080004f8   0x00000034   Code   RO         5771    .text               c_w.l(vsnprintf.o)
    0x0800052c   0x0800052c   0x0000003c   Code   RO         5773    .text               c_w.l(__0sscanf.o)
    0x08000568   0x08000568   0x0000014c   Code   RO         5775    .text               c_w.l(_scanf_int.o)
    0x080006b4   0x080006b4   0x00000080   Code   RO         5777    .text               c_w.l(assert.o)
    0x08000734   0x08000734   0x0000008a   Code   RO         5779    .text               c_w.l(rt_memcpy_v6.o)
    0x080007be   0x080007be   0x0000004e   Code   RO         5781    .text               c_w.l(rt_memclr_w.o)
    0x0800080c   0x0800080c   0x00000006   Code   RO         5783    .text               c_w.l(heapauxi.o)
    0x08000812   0x08000812   0x00000002   PAD
    0x08000814   0x08000814   0x00000008   Code   RO         5867    .text               c_w.l(rt_heap_descriptor_intlibspace.o)
    0x0800081c   0x0800081c   0x00000004   Code   RO         5869    .text               c_w.l(hguard.o)
    0x08000820   0x08000820   0x0000008a   Code   RO         5871    .text               c_w.l(init_alloc.o)
    0x080008aa   0x080008aa   0x0000000e   Code   RO         5875    .text               c_w.l(h1_init.o)
    0x080008b8   0x080008b8   0x00000016   Code   RO         5889    .text               c_w.l(_rserrno.o)
    0x080008ce   0x080008ce   0x0000004e   Code   RO         5893    .text               c_w.l(_printf_pad.o)
    0x0800091c   0x0800091c   0x00000024   Code   RO         5895    .text               c_w.l(_printf_truncate.o)
    0x08000940   0x08000940   0x00000052   Code   RO         5897    .text               c_w.l(_printf_str.o)
    0x08000992   0x08000992   0x00000002   PAD
    0x08000994   0x08000994   0x00000078   Code   RO         5899    .text               c_w.l(_printf_dec.o)
    0x08000a0c   0x08000a0c   0x00000028   Code   RO         5901    .text               c_w.l(_printf_charcount.o)
    0x08000a34   0x08000a34   0x00000030   Code   RO         5903    .text               c_w.l(_printf_char_common.o)
    0x08000a64   0x08000a64   0x0000000a   Code   RO         5905    .text               c_w.l(_sputc.o)
    0x08000a6e   0x08000a6e   0x00000010   Code   RO         5907    .text               c_w.l(_snputc.o)
    0x08000a7e   0x08000a7e   0x00000002   PAD
    0x08000a80   0x08000a80   0x000000bc   Code   RO         5909    .text               c_w.l(_printf_wctomb.o)
    0x08000b3c   0x08000b3c   0x0000007c   Code   RO         5912    .text               c_w.l(_printf_longlong_dec.o)
    0x08000bb8   0x08000bb8   0x00000070   Code   RO         5918    .text               c_w.l(_printf_oct_int_ll.o)
    0x08000c28   0x08000c28   0x00000094   Code   RO         5938    .text               c_w.l(_printf_hex_int_ll_ptr.o)
    0x08000cbc   0x08000cbc   0x00000188   Code   RO         5958    .text               c_w.l(__printf_flags_ss_wp.o)
    0x08000e44   0x08000e44   0x0000001c   Code   RO         5984    .text               c_w.l(_chval.o)
    0x08000e60   0x08000e60   0x0000002c   Code   RO         5986    .text               c_w.l(scanf_char.o)
    0x08000e8c   0x08000e8c   0x00000040   Code   RO         5988    .text               c_w.l(_sgetc.o)
    0x08000ecc   0x08000ecc   0x00000016   Code   RO         5990    .text               c_w.l(abort.o)
    0x08000ee2   0x08000ee2   0x00000014   Code   RO         5992    .text               c_w.l(assert_puts.o)
    0x08000ef6   0x08000ef6   0x00000064   Code   RO         5994    .text               c_w.l(rt_memcpy_w.o)
    0x08000f5a   0x08000f5a   0x0000000e   Code   RO         6080    .text               c_w.l(sys_wrch.o)
    0x08000f68   0x08000f68   0x0000000c   Code   RO         6084    .text               c_w.l(sys_exit.o)
    0x08000f74   0x08000f74   0x00000008   Code   RO         6086    .text               c_w.l(libspace.o)
    0x08000f7c   0x08000f7c   0x00000008   Code   RO         6109    .text               c_w.l(rt_errno_addr_intlibspace.o)
    0x08000f84   0x08000f84   0x00000034   Code   RO         6115    .text               c_w.l(h1_extend.o)
    0x08000fb8   0x08000fb8   0x0000008a   Code   RO         6119    .text               c_w.l(lludiv10.o)
    0x08001042   0x08001042   0x00000012   Code   RO         6121    .text               c_w.l(isspace.o)
    0x08001054   0x08001054   0x000000b2   Code   RO         6123    .text               c_w.l(_printf_intcommon.o)
    0x08001106   0x08001106   0x0000041e   Code   RO         6125    .text               c_w.l(_printf_fp_dec.o)
    0x08001524   0x08001524   0x000002fc   Code   RO         6127    .text               c_w.l(_printf_fp_hex.o)
    0x08001820   0x08001820   0x0000002c   Code   RO         6132    .text               c_w.l(_printf_char.o)
    0x0800184c   0x0800184c   0x0000002c   Code   RO         6134    .text               c_w.l(_printf_wchar.o)
    0x08001878   0x08001878   0x00000374   Code   RO         6137    .text               c_w.l(_scanf.o)
    0x08001bec   0x08001bec   0x00000040   Code   RO         6139    .text               c_w.l(_wcrtomb.o)
    0x08001c2c   0x08001c2c   0x0000000e   Code   RO         6141    .text               c_w.l(defsig_abrt_outer.o)
    0x08001c3a   0x08001c3a   0x0000000e   Code   RO         6145    .text               c_w.l(defsig_rtmem_outer.o)
    0x08001c48   0x08001c48   0x00000002   Code   RO         6156    .text               c_w.l(use_no_semi.o)
    0x08001c4a   0x08001c4a   0x00000000   Code   RO         6158    .text               c_w.l(indicate_semi.o)
    0x08001c4a   0x08001c4a   0x0000004a   Code   RO         6159    .text               c_w.l(sys_stackheap_outer.o)
    0x08001c94   0x08001c94   0x00000010   Code   RO         6161    .text               c_w.l(rt_ctype_table.o)
    0x08001ca4   0x08001ca4   0x00000008   Code   RO         6168    .text               c_w.l(rt_locale_intlibspace.o)
    0x08001cac   0x08001cac   0x00000080   Code   RO         6172    .text               c_w.l(_printf_fp_infnan.o)
    0x08001d2c   0x08001d2c   0x000000e4   Code   RO         6174    .text               c_w.l(bigflt0.o)
    0x08001e10   0x08001e10   0x00000012   Code   RO         6202    .text               c_w.l(exit.o)
    0x08001e22   0x08001e22   0x0000000a   Code   RO         6204    .text               c_w.l(defsig_exit.o)
    0x08001e2c   0x08001e2c   0x00000030   Code   RO         6206    .text               c_w.l(defsig_abrt_inner.o)
    0x08001e5c   0x08001e5c   0x00000050   Code   RO         6212    .text               c_w.l(defsig_rtmem_inner.o)
    0x08001eac   0x08001eac   0x00000032   Code   RO         6237    .text               c_w.l(defsig_general.o)
    0x08001ede   0x08001ede   0x00000002   PAD
    0x08001ee0   0x08001ee0   0x00000080   Code   RO         6241    .text               c_w.l(strcmpv7m.o)
    0x08001f60   0x08001f60   0x0000003e   Code   RO         6177    CL$$btod_d2e        c_w.l(btod.o)
    0x08001f9e   0x08001f9e   0x00000046   Code   RO         6179    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08001fe4   0x08001fe4   0x00000060   Code   RO         6178    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08002044   0x08002044   0x00000338   Code   RO         6187    CL$$btod_div_common  c_w.l(btod.o)
    0x0800237c   0x0800237c   0x000000dc   Code   RO         6184    CL$$btod_e2e        c_w.l(btod.o)
    0x08002458   0x08002458   0x0000002a   Code   RO         6181    CL$$btod_ediv       c_w.l(btod.o)
    0x08002482   0x08002482   0x0000002a   Code   RO         6180    CL$$btod_emul       c_w.l(btod.o)
    0x080024ac   0x080024ac   0x00000244   Code   RO         6186    CL$$btod_mult_common  c_w.l(btod.o)
    0x080026f0   0x080026f0   0x00000094   Code   RO         5237    i.Angle_PID_control  pid_app.o
    0x08002784   0x08002784   0x0000007c   Code   RO         4364    i.BNO080_HardwareReset  bno08x_hal.o
    0x08002800   0x08002800   0x00000014   Code   RO         4365    i.BNO080_Init       bno08x_hal.o
    0x08002814   0x08002814   0x00000004   Code   RO          576    i.BusFault_Handler  stm32f4xx_it.o
    0x08002818   0x08002818   0x00000178   Code   RO         5630    i.Car_State_Update  my_timer.o
    0x08002990   0x08002990   0x00000010   Code   RO          577    i.DMA1_Stream0_IRQHandler  stm32f4xx_it.o
    0x080029a0   0x080029a0   0x00000010   Code   RO          578    i.DMA2_Stream2_IRQHandler  stm32f4xx_it.o
    0x080029b0   0x080029b0   0x00000034   Code   RO         1711    i.DMA_CalcBaseAndBitshift  stm32f4xx_hal_dma.o
    0x080029e4   0x080029e4   0x000000aa   Code   RO         1712    i.DMA_CheckFifoParam  stm32f4xx_hal_dma.o
    0x08002a8e   0x08002a8e   0x0000002c   Code   RO         1713    i.DMA_SetConfig     stm32f4xx_hal_dma.o
    0x08002aba   0x08002aba   0x000000e0   Code   RO         4101    i.DRV8871_Control   motor_driver.o
    0x08002b9a   0x08002b9a   0x00000002   Code   RO          579    i.DebugMon_Handler  stm32f4xx_it.o
    0x08002b9c   0x08002b9c   0x00000058   Code   RO         3886    i.Delay_us          software_iic.o
    0x08002bf4   0x08002bf4   0x0000000a   Code   RO          580    i.EXTI3_IRQHandler  stm32f4xx_it.o
    0x08002bfe   0x08002bfe   0x00000002   PAD
    0x08002c00   0x08002c00   0x00000030   Code   RO         4319    i.Encoder_Driver_Init  encoder_driver.o
    0x08002c30   0x08002c30   0x00000090   Code   RO         4320    i.Encoder_Driver_Update  encoder_driver.o
    0x08002cc0   0x08002cc0   0x00000028   Code   RO         5197    i.Encoder_Init      encoder_app.o
    0x08002ce8   0x08002ce8   0x00000018   Code   RO         5198    i.Encoder_Task      encoder_app.o
    0x08002d00   0x08002d00   0x00000006   Code   RO           13    i.Error_Handler     main.o
    0x08002d06   0x08002d06   0x0000002a   Code   RO         4102    i.Float_To_Speed1000  motor_driver.o
    0x08002d30   0x08002d30   0x0000006c   Code   RO         5290    i.Gray_Init         gray_app.o
    0x08002d9c   0x08002d9c   0x0000007c   Code   RO         5291    i.Gray_Task         gray_app.o
    0x08002e18   0x08002e18   0x000000ac   Code   RO         1714    i.HAL_DMA_Abort     stm32f4xx_hal_dma.o
    0x08002ec4   0x08002ec4   0x00000028   Code   RO         1715    i.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x08002eec   0x08002eec   0x00000240   Code   RO         1719    i.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x0800312c   0x0800312c   0x000000ec   Code   RO         1720    i.HAL_DMA_Init      stm32f4xx_hal_dma.o
    0x08003218   0x08003218   0x00000092   Code   RO         1724    i.HAL_DMA_Start_IT  stm32f4xx_hal_dma.o
    0x080032aa   0x080032aa   0x00000002   PAD
    0x080032ac   0x080032ac   0x00000028   Code   RO         2156    i.HAL_Delay         stm32f4xx_hal.o
    0x080032d4   0x080032d4   0x00000002   Code   RO         1605    i.HAL_GPIO_EXTI_Callback  stm32f4xx_hal_gpio.o
    0x080032d6   0x080032d6   0x00000002   PAD
    0x080032d8   0x080032d8   0x0000001c   Code   RO         1606    i.HAL_GPIO_EXTI_IRQHandler  stm32f4xx_hal_gpio.o
    0x080032f4   0x080032f4   0x000001f4   Code   RO         1607    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x080034e8   0x080034e8   0x00000010   Code   RO         1609    i.HAL_GPIO_ReadPin  stm32f4xx_hal_gpio.o
    0x080034f8   0x080034f8   0x0000000c   Code   RO         1611    i.HAL_GPIO_WritePin  stm32f4xx_hal_gpio.o
    0x08003504   0x08003504   0x0000000c   Code   RO         2162    i.HAL_GetTick       stm32f4xx_hal.o
    0x08003510   0x08003510   0x000001d0   Code   RO          723    i.HAL_I2C_Init      stm32f4xx_hal_i2c.o
    0x080036e0   0x080036e0   0x0000030c   Code   RO          729    i.HAL_I2C_Master_Receive  stm32f4xx_hal_i2c.o
    0x080039ec   0x080039ec   0x00000170   Code   RO          736    i.HAL_I2C_Master_Transmit  stm32f4xx_hal_i2c.o
    0x08003b5c   0x08003b5c   0x00000164   Code   RO          744    i.HAL_I2C_Mem_Write  stm32f4xx_hal_i2c.o
    0x08003cc0   0x08003cc0   0x000000f4   Code   RO          403    i.HAL_I2C_MspInit   i2c.o
    0x08003db4   0x08003db4   0x00000018   Code   RO         2168    i.HAL_IncTick       stm32f4xx_hal.o
    0x08003dcc   0x08003dcc   0x0000003c   Code   RO         2169    i.HAL_Init          stm32f4xx_hal.o
    0x08003e08   0x08003e08   0x0000004c   Code   RO         2170    i.HAL_InitTick      stm32f4xx_hal.o
    0x08003e54   0x08003e54   0x00000050   Code   RO          688    i.HAL_MspInit       stm32f4xx_hal_msp.o
    0x08003ea4   0x08003ea4   0x00000028   Code   RO         1999    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x08003ecc   0x08003ecc   0x0000007c   Code   RO         2005    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08003f48   0x08003f48   0x00000028   Code   RO         2006    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08003f70   0x08003f70   0x00000184   Code   RO         1208    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x080040f4   0x080040f4   0x0000000c   Code   RO         1213    i.HAL_RCC_GetHCLKFreq  stm32f4xx_hal_rcc.o
    0x08004100   0x08004100   0x00000020   Code   RO         1215    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x08004120   0x08004120   0x00000020   Code   RO         1216    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x08004140   0x08004140   0x000000b0   Code   RO         1217    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x080041f0   0x080041f0   0x0000049c   Code   RO         1220    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x0800468c   0x0800468c   0x00000034   Code   RO         2010    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x080046c0   0x080046c0   0x00000002   Code   RO         3124    i.HAL_TIMEx_BreakCallback  stm32f4xx_hal_tim_ex.o
    0x080046c2   0x080046c2   0x00000002   Code   RO         3125    i.HAL_TIMEx_CommutCallback  stm32f4xx_hal_tim_ex.o
    0x080046c4   0x080046c4   0x00000074   Code   RO         3127    i.HAL_TIMEx_ConfigBreakDeadTime  stm32f4xx_hal_tim_ex.o
    0x08004738   0x08004738   0x000000b4   Code   RO         3143    i.HAL_TIMEx_MasterConfigSynchronization  stm32f4xx_hal_tim_ex.o
    0x080047ec   0x080047ec   0x00000066   Code   RO         2409    i.HAL_TIM_Base_Init  stm32f4xx_hal_tim.o
    0x08004852   0x08004852   0x00000002   PAD
    0x08004854   0x08004854   0x00000074   Code   RO          451    i.HAL_TIM_Base_MspInit  tim.o
    0x080048c8   0x080048c8   0x000000a8   Code   RO         2414    i.HAL_TIM_Base_Start_IT  stm32f4xx_hal_tim.o
    0x08004970   0x08004970   0x0000010c   Code   RO         2418    i.HAL_TIM_ConfigClockSource  stm32f4xx_hal_tim.o
    0x08004a7c   0x08004a7c   0x000000c8   Code   RO         2430    i.HAL_TIM_Encoder_Init  stm32f4xx_hal_tim.o
    0x08004b44   0x08004b44   0x000000f8   Code   RO          453    i.HAL_TIM_Encoder_MspInit  tim.o
    0x08004c3c   0x08004c3c   0x000000cc   Code   RO         2433    i.HAL_TIM_Encoder_Start  stm32f4xx_hal_tim.o
    0x08004d08   0x08004d08   0x00000002   Code   RO         2443    i.HAL_TIM_IC_CaptureCallback  stm32f4xx_hal_tim.o
    0x08004d0a   0x08004d0a   0x0000016c   Code   RO         2457    i.HAL_TIM_IRQHandler  stm32f4xx_hal_tim.o
    0x08004e76   0x08004e76   0x00000002   PAD
    0x08004e78   0x08004e78   0x00000064   Code   RO          454    i.HAL_TIM_MspPostInit  tim.o
    0x08004edc   0x08004edc   0x00000002   Code   RO         2460    i.HAL_TIM_OC_DelayElapsedCallback  stm32f4xx_hal_tim.o
    0x08004ede   0x08004ede   0x00000104   Code   RO         2481    i.HAL_TIM_PWM_ConfigChannel  stm32f4xx_hal_tim.o
    0x08004fe2   0x08004fe2   0x00000066   Code   RO         2484    i.HAL_TIM_PWM_Init  stm32f4xx_hal_tim.o
    0x08005048   0x08005048   0x00000002   Code   RO         2486    i.HAL_TIM_PWM_MspInit  stm32f4xx_hal_tim.o
    0x0800504a   0x0800504a   0x00000002   Code   RO         2487    i.HAL_TIM_PWM_PulseFinishedCallback  stm32f4xx_hal_tim.o
    0x0800504c   0x0800504c   0x0000010c   Code   RO         2489    i.HAL_TIM_PWM_Start  stm32f4xx_hal_tim.o
    0x08005158   0x08005158   0x00000144   Code   RO         5631    i.HAL_TIM_PeriodElapsedCallback  my_timer.o
    0x0800529c   0x0800529c   0x00000002   Code   RO         2500    i.HAL_TIM_TriggerCallback  stm32f4xx_hal_tim.o
    0x0800529e   0x0800529e   0x00000070   Code   RO         3401    i.HAL_UARTEx_ReceiveToIdle_DMA  stm32f4xx_hal_uart.o
    0x0800530e   0x0800530e   0x00000002   PAD
    0x08005310   0x08005310   0x00000058   Code   RO         4815    i.HAL_UARTEx_RxEventCallback  usart_app.o
    0x08005368   0x08005368   0x0000008a   Code   RO         3415    i.HAL_UART_DMAStop  stm32f4xx_hal_uart.o
    0x080053f2   0x080053f2   0x00000002   Code   RO         3417    i.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x080053f4   0x080053f4   0x00000308   Code   RO         3420    i.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x080056fc   0x080056fc   0x00000076   Code   RO         3421    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x08005772   0x08005772   0x00000002   PAD
    0x08005774   0x08005774   0x000001e0   Code   RO          529    i.HAL_UART_MspInit  usart.o
    0x08005954   0x08005954   0x00000002   Code   RO         3427    i.HAL_UART_RxCpltCallback  stm32f4xx_hal_uart.o
    0x08005956   0x08005956   0x00000002   Code   RO         3428    i.HAL_UART_RxHalfCpltCallback  stm32f4xx_hal_uart.o
    0x08005958   0x08005958   0x000000be   Code   RO         3429    i.HAL_UART_Transmit  stm32f4xx_hal_uart.o
    0x08005a16   0x08005a16   0x00000002   Code   RO         3432    i.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x08005a18   0x08005a18   0x00000004   Code   RO          581    i.HardFault_Handler  stm32f4xx_it.o
    0x08005a1c   0x08005a1c   0x0000003e   Code   RO          767    i.I2C_IsAcknowledgeFailed  stm32f4xx_hal_i2c.o
    0x08005a5a   0x08005a5a   0x00000002   PAD
    0x08005a5c   0x08005a5c   0x00000138   Code   RO          770    i.I2C_MasterRequestRead  stm32f4xx_hal_i2c.o
    0x08005b94   0x08005b94   0x000000c0   Code   RO          771    i.I2C_MasterRequestWrite  stm32f4xx_hal_i2c.o
    0x08005c54   0x08005c54   0x000000e0   Code   RO          779    i.I2C_RequestMemoryWrite  stm32f4xx_hal_i2c.o
    0x08005d34   0x08005d34   0x00000066   Code   RO          787    i.I2C_WaitOnBTFFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08005d9a   0x08005d9a   0x000000be   Code   RO          788    i.I2C_WaitOnFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08005e58   0x08005e58   0x000000fa   Code   RO          789    i.I2C_WaitOnMasterAddressFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08005f52   0x08005f52   0x0000008a   Code   RO          790    i.I2C_WaitOnRXNEFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08005fdc   0x08005fdc   0x00000066   Code   RO          793    i.I2C_WaitOnTXEFlagUntilTimeout  stm32f4xx_hal_i2c.o
    0x08006042   0x08006042   0x0000000a   Code   RO         3888    i.IIC_Delay         software_iic.o
    0x0800604c   0x0800604c   0x00000020   Code   RO         3890    i.IIC_Get_Digtal    software_iic.o
    0x0800606c   0x0800606c   0x00000076   Code   RO         3894    i.IIC_ReadBytes     software_iic.o
    0x080060e2   0x080060e2   0x00000002   PAD
    0x080060e4   0x080060e4   0x0000007c   Code   RO         3895    i.IIC_RecvByte      software_iic.o
    0x08006160   0x08006160   0x00000034   Code   RO         3896    i.IIC_SendAck       software_iic.o
    0x08006194   0x08006194   0x00000058   Code   RO         3897    i.IIC_SendByte      software_iic.o
    0x080061ec   0x080061ec   0x0000002c   Code   RO         3898    i.IIC_SendNAck      software_iic.o
    0x08006218   0x08006218   0x00000038   Code   RO         3899    i.IIC_Start         software_iic.o
    0x08006250   0x08006250   0x00000034   Code   RO         3900    i.IIC_Stop          software_iic.o
    0x08006284   0x08006284   0x0000003c   Code   RO         3901    i.IIC_WaitAck       software_iic.o
    0x080062c0   0x080062c0   0x00000088   Code   RO         5238    i.Line_PID_control  pid_app.o
    0x08006348   0x08006348   0x00000068   Code   RO          375    i.MX_DMA_Init       dma.o
    0x080063b0   0x080063b0   0x000001cc   Code   RO          252    i.MX_GPIO_Init      gpio.o
    0x0800657c   0x0800657c   0x0000003c   Code   RO          404    i.MX_I2C1_Init      i2c.o
    0x080065b8   0x080065b8   0x0000003c   Code   RO          405    i.MX_I2C2_Init      i2c.o
    0x080065f4   0x080065f4   0x00000108   Code   RO          455    i.MX_TIM1_Init      tim.o
    0x080066fc   0x080066fc   0x0000006c   Code   RO          456    i.MX_TIM2_Init      tim.o
    0x08006768   0x08006768   0x00000080   Code   RO          457    i.MX_TIM3_Init      tim.o
    0x080067e8   0x080067e8   0x00000080   Code   RO          458    i.MX_TIM4_Init      tim.o
    0x08006868   0x08006868   0x00000038   Code   RO          530    i.MX_UART5_Init     usart.o
    0x080068a0   0x080068a0   0x00000038   Code   RO          531    i.MX_USART1_UART_Init  usart.o
    0x080068d8   0x080068d8   0x00000004   Code   RO          582    i.MemManage_Handler  stm32f4xx_it.o
    0x080068dc   0x080068dc   0x000000c4   Code   RO         4103    i.Motor_Create      motor_driver.o
    0x080069a0   0x080069a0   0x00000060   Code   RO         5149    i.Motor_Init        motor_app.o
    0x08006a00   0x08006a00   0x000000a0   Code   RO         4107    i.Motor_SetSpeed    motor_driver.o
    0x08006aa0   0x08006aa0   0x00000030   Code   RO         4109    i.Motor_ValidateFloatSpeed  motor_driver.o
    0x08006ad0   0x08006ad0   0x0000001a   Code   RO         4110    i.Motor_ValidateParams  motor_driver.o
    0x08006aea   0x08006aea   0x00000004   Code   RO          583    i.NMI_Handler       stm32f4xx_it.o
    0x08006aee   0x08006aee   0x00000038   Code   RO         4193    i.OLED_Clear        oled.o
    0x08006b26   0x08006b26   0x00000002   PAD
    0x08006b28   0x08006b28   0x00000030   Code   RO         4196    i.OLED_Init         oled.o
    0x08006b58   0x08006b58   0x00000024   Code   RO         4198    i.OLED_Set_Position  oled.o
    0x08006b7c   0x08006b7c   0x0000009c   Code   RO         4199    i.OLED_ShowChar     oled.o
    0x08006c18   0x08006c18   0x0000003a   Code   RO         4205    i.OLED_ShowStr      oled.o
    0x08006c52   0x08006c52   0x00000002   PAD
    0x08006c54   0x08006c54   0x00000024   Code   RO         4206    i.OLED_Write_cmd    oled.o
    0x08006c78   0x08006c78   0x00000024   Code   RO         4207    i.OLED_Write_data   oled.o
    0x08006c9c   0x08006c9c   0x00000110   Code   RO         5239    i.PID_Init          pid_app.o
    0x08006dac   0x08006dac   0x00000154   Code   RO         5240    i.PID_Task          pid_app.o
    0x08006f00   0x08006f00   0x00000002   Code   RO          584    i.PendSV_Handler    stm32f4xx_it.o
    0x08006f02   0x08006f02   0x0000001e   Code   RO         3904    i.Ping              software_iic.o
    0x08006f20   0x08006f20   0x000001f4   Code   RO         4366    i.QuaternionToEulerAngles  bno08x_hal.o
    0x08007114   0x08007114   0x00000002   Code   RO          585    i.SVC_Handler       stm32f4xx_it.o
    0x08007116   0x08007116   0x00000066   Code   RO         4111    i.Set_Pin_Mode      motor_driver.o
    0x0800717c   0x0800717c   0x00000058   Code   RO         4112    i.Speed1000_To_PWM  motor_driver.o
    0x080071d4   0x080071d4   0x00000008   Code   RO          586    i.SysTick_Handler   stm32f4xx_it.o
    0x080071dc   0x080071dc   0x000000b4   Code   RO           14    i.SystemClock_Config  main.o
    0x08007290   0x08007290   0x00000014   Code   RO         3765    i.SystemInit        system_stm32f4xx.o
    0x080072a4   0x080072a4   0x00000010   Code   RO          587    i.TIM2_IRQHandler   stm32f4xx_it.o
    0x080072b4   0x080072b4   0x000000e0   Code   RO         2502    i.TIM_Base_SetConfig  stm32f4xx_hal_tim.o
    0x08007394   0x08007394   0x00000022   Code   RO         2503    i.TIM_CCxChannelCmd  stm32f4xx_hal_tim.o
    0x080073b6   0x080073b6   0x00000016   Code   RO         2513    i.TIM_ETR_SetConfig  stm32f4xx_hal_tim.o
    0x080073cc   0x080073cc   0x00000012   Code   RO         2514    i.TIM_ITRx_SetConfig  stm32f4xx_hal_tim.o
    0x080073de   0x080073de   0x00000002   PAD
    0x080073e0   0x080073e0   0x00000070   Code   RO         2515    i.TIM_OC1_SetConfig  stm32f4xx_hal_tim.o
    0x08007450   0x08007450   0x0000007c   Code   RO         2516    i.TIM_OC2_SetConfig  stm32f4xx_hal_tim.o
    0x080074cc   0x080074cc   0x00000078   Code   RO         2517    i.TIM_OC3_SetConfig  stm32f4xx_hal_tim.o
    0x08007544   0x08007544   0x00000054   Code   RO         2518    i.TIM_OC4_SetConfig  stm32f4xx_hal_tim.o
    0x08007598   0x08007598   0x00000026   Code   RO         2520    i.TIM_TI1_ConfigInputStage  stm32f4xx_hal_tim.o
    0x080075be   0x080075be   0x00000028   Code   RO         2522    i.TIM_TI2_ConfigInputStage  stm32f4xx_hal_tim.o
    0x080075e6   0x080075e6   0x00000002   PAD
    0x080075e8   0x080075e8   0x00000010   Code   RO          588    i.UART5_IRQHandler  stm32f4xx_it.o
    0x080075f8   0x080075f8   0x00000012   Code   RO         3434    i.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x0800760a   0x0800760a   0x00000050   Code   RO         3435    i.UART_DMAError     stm32f4xx_hal_uart.o
    0x0800765a   0x0800765a   0x000000b4   Code   RO         3436    i.UART_DMAReceiveCplt  stm32f4xx_hal_uart.o
    0x0800770e   0x0800770e   0x00000024   Code   RO         3438    i.UART_DMARxHalfCplt  stm32f4xx_hal_uart.o
    0x08007732   0x08007732   0x0000006c   Code   RO         3444    i.UART_EndRxTransfer  stm32f4xx_hal_uart.o
    0x0800779e   0x0800779e   0x00000020   Code   RO         3445    i.UART_EndTransmit_IT  stm32f4xx_hal_uart.o
    0x080077be   0x080077be   0x00000026   Code   RO         3446    i.UART_EndTxTransfer  stm32f4xx_hal_uart.o
    0x080077e4   0x080077e4   0x000000fc   Code   RO         3447    i.UART_Receive_IT   stm32f4xx_hal_uart.o
    0x080078e0   0x080078e0   0x0000022c   Code   RO         3448    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x08007b0c   0x08007b0c   0x000000d8   Code   RO         3449    i.UART_Start_Receive_DMA  stm32f4xx_hal_uart.o
    0x08007be4   0x08007be4   0x00000060   Code   RO         3451    i.UART_Transmit_IT  stm32f4xx_hal_uart.o
    0x08007c44   0x08007c44   0x0000008c   Code   RO         3452    i.UART_WaitOnFlagUntilTimeout  stm32f4xx_hal_uart.o
    0x08007cd0   0x08007cd0   0x00000010   Code   RO          589    i.USART1_IRQHandler  stm32f4xx_it.o
    0x08007ce0   0x08007ce0   0x00000004   Code   RO          590    i.UsageFault_Handler  stm32f4xx_it.o
    0x08007ce4   0x08007ce4   0x00000030   Code   RO         6224    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x08007d14   0x08007d14   0x00000026   Code   RO         6064    i.__ARM_fpclassifyf  m_wm.l(fpclassifyf.o)
    0x08007d3a   0x08007d3a   0x00000002   PAD
    0x08007d3c   0x08007d3c   0x00000010   Code   RO         2012    i.__NVIC_GetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08007d4c   0x08007d4c   0x00000028   Code   RO         2013    i.__NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08007d74   0x08007d74   0x0000012c   Code   RO         5803    i.__hardfp_asinf    m_wm.l(asinf.o)
    0x08007ea0   0x08007ea0   0x000002ac   Code   RO         5815    i.__hardfp_atan2f   m_wm.l(atan2f.o)
    0x0800814c   0x0800814c   0x00000664   Code   RO         5827    i.__hardfp_powf     m_wm.l(powf.o)
    0x080087b0   0x080087b0   0x0000009a   Code   RO         5853    i.__hardfp_roundf   m_wm.l(roundf.o)
    0x0800884a   0x0800884a   0x0000003a   Code   RO         5841    i.__hardfp_sqrtf    m_wm.l(sqrtf.o)
    0x08008884   0x08008884   0x00000014   Code   RO         6066    i.__mathlib_flt_divzero  m_wm.l(funder.o)
    0x08008898   0x08008898   0x00000006   Code   RO         6067    i.__mathlib_flt_infnan  m_wm.l(funder.o)
    0x0800889e   0x0800889e   0x00000006   Code   RO         6068    i.__mathlib_flt_infnan2  m_wm.l(funder.o)
    0x080088a4   0x080088a4   0x00000010   Code   RO         6069    i.__mathlib_flt_invalid  m_wm.l(funder.o)
    0x080088b4   0x080088b4   0x00000010   Code   RO         6070    i.__mathlib_flt_overflow  m_wm.l(funder.o)
    0x080088c4   0x080088c4   0x00000010   Code   RO         6072    i.__mathlib_flt_underflow  m_wm.l(funder.o)
    0x080088d4   0x080088d4   0x0000000e   Code   RO         5951    i._is_digit         c_w.l(__printf_wp.o)
    0x080088e2   0x080088e2   0x00000002   PAD
    0x080088e4   0x080088e4   0x00000034   Code   RO         5558    i.all_task_init     my_scheduler.o
    0x08008918   0x08008918   0x00000058   Code   RO         5559    i.all_task_run      my_scheduler.o
    0x08008970   0x08008970   0x00000090   Code   RO         5494    i.bno080_task       bno08x_app.o
    0x08008a00   0x08008a00   0x00000098   Code   RO         5495    i.convert_to_continuous_yaw  bno08x_app.o
    0x08008a98   0x08008a98   0x00000030   Code   RO         4372    i.dataAvailable     bno08x_hal.o
    0x08008ac8   0x08008ac8   0x00000010   Code   RO         4378    i.enableRotationVector  bno08x_hal.o
    0x08008ad8   0x08008ad8   0x0000001c   Code   RO         4404    i.getQuatI          bno08x_hal.o
    0x08008af4   0x08008af4   0x0000001c   Code   RO         4405    i.getQuatJ          bno08x_hal.o
    0x08008b10   0x08008b10   0x0000001c   Code   RO         4406    i.getQuatK          bno08x_hal.o
    0x08008b2c   0x08008b2c   0x0000001c   Code   RO         4408    i.getQuatReal       bno08x_hal.o
    0x08008b48   0x08008b48   0x0000001c   Code   RO         5498    i.get_yaw           bno08x_app.o
    0x08008b64   0x08008b64   0x0000004c   Code   RO         4788    i.key_read          key_driver.o
    0x08008bb0   0x08008bb0   0x0000008c   Code   RO         4914    i.key_task          key_app.o
    0x08008c3c   0x08008c3c   0x00000084   Code   RO         4760    i.led_disp          led_driver.o
    0x08008cc0   0x08008cc0   0x00000002   Code   RO         4878    i.led_init          led_app.o
    0x08008cc2   0x08008cc2   0x00000002   PAD
    0x08008cc4   0x08008cc4   0x00000010   Code   RO         4879    i.led_task          led_app.o
    0x08008cd4   0x08008cd4   0x0000003c   Code   RO           15    i.main              main.o
    0x08008d10   0x08008d10   0x00000020   Code   RO         5151    i.motor_set_l       motor_app.o
    0x08008d30   0x08008d30   0x00000020   Code   RO         5152    i.motor_set_r       motor_app.o
    0x08008d50   0x08008d50   0x00000124   Code   RO         5499    i.my_bno080_init    bno08x_app.o
    0x08008e74   0x08008e74   0x00000008   Code   RO         5107    i.my_oled_init      oled_app.o
    0x08008e7c   0x08008e7c   0x0000003a   Code   RO         4816    i.my_printf         usart_app.o
    0x08008eb6   0x08008eb6   0x0000003c   Code   RO         5108    i.oled_printf       oled_app.o
    0x08008ef2   0x08008ef2   0x00000002   PAD
    0x08008ef4   0x08008ef4   0x000000c4   Code   RO         5109    i.oled_task         oled_app.o
    0x08008fb8   0x08008fb8   0x000001e0   Code   RO         4413    i.parseInputReport  bno08x_hal.o
    0x08009198   0x08009198   0x00000026   Code   RO         4022    i.pid_calculate_positional  pid.o
    0x080091be   0x080091be   0x0000002a   Code   RO         4023    i.pid_constrain     pid.o
    0x080091e8   0x080091e8   0x0000007a   Code   RO         4025    i.pid_formula_positional  pid.o
    0x08009262   0x08009262   0x00000002   PAD
    0x08009264   0x08009264   0x00000054   Code   RO         4026    i.pid_init          pid.o
    0x080092b8   0x080092b8   0x00000040   Code   RO         4027    i.pid_out_limit     pid.o
    0x080092f8   0x080092f8   0x00000040   Code   RO         4028    i.pid_reset         pid.o
    0x08009338   0x08009338   0x00000006   Code   RO         4031    i.pid_set_target    pid.o
    0x0800933e   0x0800933e   0x0000003a   Code   RO         4414    i.qToFloat          bno08x_hal.o
    0x08009378   0x08009378   0x000000c4   Code   RO         4417    i.receivePacket     bno08x_hal.o
    0x0800943c   0x0800943c   0x0000003c   Code   RO         3798    i.rt_ringbuffer_data_len  ringbuffer.o
    0x08009478   0x08009478   0x000000ec   Code   RO         3799    i.rt_ringbuffer_get  ringbuffer.o
    0x08009564   0x08009564   0x00000094   Code   RO         3801    i.rt_ringbuffer_init  ringbuffer.o
    0x080095f8   0x080095f8   0x000000f0   Code   RO         3803    i.rt_ringbuffer_put  ringbuffer.o
    0x080096e8   0x080096e8   0x0000002a   Code   RO         3808    i.rt_ringbuffer_status  ringbuffer.o
    0x08009712   0x08009712   0x00000002   PAD
    0x08009714   0x08009714   0x00000080   Code   RO         4422    i.sendPacket        bno08x_hal.o
    0x08009794   0x08009794   0x0000005c   Code   RO         4423    i.setFeatureCommand  bno08x_hal.o
    0x080097f0   0x080097f0   0x00000038   Code   RO         4424    i.softReset         bno08x_hal.o
    0x08009828   0x08009828   0x0000003e   Code   RO         5843    i.sqrtf             m_wm.l(sqrtf.o)
    0x08009866   0x08009866   0x00000002   PAD
    0x08009868   0x08009868   0x00000010   Code   RO         5632    i.timer_init        my_timer.o
    0x08009878   0x08009878   0x0000003c   Code   RO         4817    i.uart_init         usart_app.o
    0x080098b4   0x080098b4   0x000000f8   Code   RO         4818    i.uart_task         usart_app.o
    0x080099ac   0x080099ac   0x0000002c   Code   RO         6200    locale$$code        c_w.l(lc_numeric_c.o)
    0x080099d8   0x080099d8   0x0000002c   Code   RO         6233    locale$$code        c_w.l(lc_ctype_c.o)
    0x08009a04   0x08009a04   0x00000062   Code   RO         5787    x$fpl$d2f           fz_wm.l(d2f.o)
    0x08009a66   0x08009a66   0x00000002   PAD
    0x08009a68   0x08009a68   0x000002b0   Code   RO         5790    x$fpl$ddiv          fz_wm.l(ddiv.o)
    0x08009d18   0x08009d18   0x0000002e   Code   RO         5794    x$fpl$dflt          fz_wm.l(dflt_clz.o)
    0x08009d46   0x08009d46   0x00000002   PAD
    0x08009d48   0x08009d48   0x00000154   Code   RO         5799    x$fpl$dmul          fz_wm.l(dmul.o)
    0x08009e9c   0x08009e9c   0x0000009c   Code   RO         6043    x$fpl$dnaninf       fz_wm.l(dnaninf.o)
    0x08009f38   0x08009f38   0x0000000c   Code   RO         6045    x$fpl$dretinf       fz_wm.l(dretinf.o)
    0x08009f44   0x08009f44   0x00000056   Code   RO         5801    x$fpl$f2d           fz_wm.l(f2d.o)
    0x08009f9a   0x08009f9a   0x0000008c   Code   RO         6047    x$fpl$fnaninf       fz_wm.l(fnaninf.o)
    0x0800a026   0x0800a026   0x0000000a   Code   RO         6154    x$fpl$fpinit        fz_wm.l(fpinit.o)
    0x0800a030   0x0800a030   0x0000000a   Code   RO         6049    x$fpl$fretinf       fz_wm.l(fretinf.o)
    0x0800a03a   0x0800a03a   0x00000002   PAD
    0x0800a03c   0x0800a03c   0x00000060   Code   RO         6051    x$fpl$frnd          fz_wm.l(frnd.o)
    0x0800a09c   0x0800a09c   0x00000004   Code   RO         6055    x$fpl$printf1       fz_wm.l(printf1.o)
    0x0800a0a0   0x0800a0a0   0x00000004   Code   RO         6057    x$fpl$printf2       fz_wm.l(printf2.o)
    0x0800a0a4   0x0800a0a4   0x00000000   Code   RO         6063    x$fpl$usenofp       fz_wm.l(usenofp.o)
    0x0800a0a4   0x0800a0a4   0x00000008   Data   RO         1726    .constdata          stm32f4xx_hal_dma.o
    0x0800a0ac   0x0800a0ac   0x00000018   Data   RO         3766    .constdata          system_stm32f4xx.o
    0x0800a0c4   0x0800a0c4   0x00000a98   Data   RO         4208    .constdata          oled.o
    0x0800ab5c   0x0800ab5c   0x00000140   Data   RO         5830    .constdata          m_wm.l(powf.o)
    0x0800ac9c   0x0800ac9c   0x00000008   Data   RO         5910    .constdata          c_w.l(_printf_wctomb.o)
    0x0800aca4   0x0800aca4   0x00000028   Data   RO         5939    .constdata          c_w.l(_printf_hex_int_ll_ptr.o)
    0x0800accc   0x0800accc   0x00000011   Data   RO         5959    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x0800acdd   0x0800acdd   0x00000026   Data   RO         6128    .constdata          c_w.l(_printf_fp_hex.o)
    0x0800ad03   0x0800ad03   0x00000001   PAD
    0x0800ad04   0x0800ad04   0x00000094   Data   RO         6175    .constdata          c_w.l(bigflt0.o)
    0x0800ad98   0x0800ad98   0x00000020   Data   RO         6285    Region$$Table       anon$$obj.o
    0x0800adb8   0x0800adb8   0x0000001c   Data   RO         6199    locale$$data        c_w.l(lc_numeric_c.o)
    0x0800add4   0x0800add4   0x00000110   Data   RO         6232    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800aee4, Size: 0x000038a0, Max: 0x0001c000, ABSOLUTE, COMPRESSED[0x0000009c])

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   COMPRESSED   0x00000009   Data   RW         2176    .data               stm32f4xx_hal.o
    0x20000009   COMPRESSED   0x00000003   PAD
    0x2000000c   COMPRESSED   0x00000004   Data   RW         3767    .data               system_stm32f4xx.o
    0x20000010   COMPRESSED   0x00000016   Data   RW         4209    .data               oled.o
    0x20000026   COMPRESSED   0x00000002   PAD
    0x20000028   COMPRESSED   0x00000058   Data   RW         4426    .data               bno08x_hal.o
    0x20000080   COMPRESSED   0x00000001   Data   RW         4761    .data               led_driver.o
    0x20000081   COMPRESSED   0x00000005   Data   RW         4880    .data               led_app.o
    0x20000086   COMPRESSED   0x00000006   Data   RW         4915    .data               key_app.o
    0x2000008c   COMPRESSED   0x00000058   Data   RW         5242    .data               pid_app.o
    0x200000e4   COMPRESSED   0x00000028   Data   RW         5292    .data               gray_app.o
    0x2000010c   COMPRESSED   0x0000001d   Data   RW         5500    .data               bno08x_app.o
    0x20000129   COMPRESSED   0x00000003   PAD
    0x2000012c   COMPRESSED   0x00000031   Data   RW         5560    .data               my_scheduler.o
    0x2000015d   COMPRESSED   0x00000003   PAD
    0x20000160   COMPRESSED   0x0000001c   Data   RW         5633    .data               my_timer.o
    0x2000017c        -       0x000000a8   Zero   RW          406    .bss                i2c.o
    0x20000224        -       0x00000120   Zero   RW          459    .bss                tim.o
    0x20000344        -       0x00000150   Zero   RW          532    .bss                usart.o
    0x20000494        -       0x000000ae   Zero   RW         4425    .bss                bno08x_hal.o
    0x20000542   COMPRESSED   0x00000002   PAD
    0x20000544        -       0x0000018c   Zero   RW         4819    .bss                usart_app.o
    0x200006d0        -       0x00000060   Zero   RW         5153    .bss                motor_app.o
    0x20000730        -       0x00000020   Zero   RW         5199    .bss                encoder_app.o
    0x20000750        -       0x000000f0   Zero   RW         5241    .bss                pid_app.o
    0x20000840        -       0x00000060   Zero   RW         6087    .bss                c_w.l(libspace.o)
    0x200008a0        -       0x00001000   Zero   RW            2    HEAP                startup_stm32f407xx.o
    0x200018a0        -       0x00002000   Zero   RW            1    STACK               startup_stm32f407xx.o


    Execution Region RW_IRAM2 (Exec base: 0x2001c000, Load base: 0x0800af80, Size: 0x00000000, Max: 0x00004000, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       616        244          0         29          0       3318   bno08x_app.o
      1830        258          0         88        174      13150   bno08x_hal.o
       104          6          0          0          0        926   dma.o
        64         24          0          0         32       2039   encoder_app.o
       192         24          0          0          0       2591   encoder_driver.o
       460         20          0          0          0     696239   gpio.o
       232         96          0         40          0     585949   gray_app.o
       364         40          0          0        168       2586   i2c.o
       140         20          0          6          0     608238   key_app.o
        76          8          0          0          0        611   key_driver.o
        18          6          0          5          0       1588   led_app.o
       132         12          0          1          0       1007   led_driver.o
       246         10          0          0          0     694972   main.o
       160         28          0          0         96       2130   motor_app.o
       886         16          0          0          0       8540   motor_driver.o
       140         16          0         49          0       1947   my_scheduler.o
       716        106          0         28          0       3196   my_timer.o
       426         22       2712         22          0       5935   oled.o
       264         48          0          0          0       1961   oled_app.o
       420         12          0          0          0       5527   pid.o
       896        154          0         88        240       3949   pid_app.o
       726        178          0          0          0       6687   ringbuffer.o
       754         40          0          0          0       8135   software_iic.o
        64         26        392          0      12288        892   startup_stm32f407xx.o
       212         36          0          9          0       9853   stm32f4xx_hal.o
       312         22          0          0          0      34755   stm32f4xx_hal_cortex.o
      1436         16          8          0          0       7534   stm32f4xx_hal_dma.o
       558         50          0          0          0       4331   stm32f4xx_hal_gpio.o
      3540         62          0          0          0      14610   stm32f4xx_hal_i2c.o
        80          6          0          0          0        978   stm32f4xx_hal_msp.o
      1820         84          0          0          0       6032   stm32f4xx_hal_rcc.o
      2762        142          0          0          0      20003   stm32f4xx_hal_tim.o
       300         30          0          0          0       3633   stm32f4xx_hal_tim_ex.o
      3094         28          0          0          0      18372   stm32f4xx_hal_uart.o
       124         30          0          0          0       8066   stm32f4xx_it.o
        20          6         24          4          0       1295   system_stm32f4xx.o
      1092         78          0          0        288       5925   tim.o
       592         60          0          0        336       2882   usart.o
       454        122          0          0        396       6562   usart_app.o

    ----------------------------------------------------------------------
     26356       <USER>       <GROUP>        380      14020    2806944   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        34          0          0         11          2          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        60          8          0          0          0         84   __0sscanf.o
        90          0          0          0          0          0   __dczerorl2.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        28          0          0          0          0          0   __scatter_zi.o
        28          0          0          0          0         68   _chval.o
         6          0          0          0          0          0   _printf_a.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        40          0          0          0          0         68   _printf_charcount.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_e.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       764          8         38          0          0        100   _printf_fp_hex.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         6          0          0          0          0          0   _printf_g.o
       148          4         40          0          0        160   _printf_hex_int_ll_ptr.o
         6          0          0          0          0          0   _printf_i.o
       178          0          0          0          0         88   _printf_intcommon.o
        10          0          0          0          0          0   _printf_l.o
         6          0          0          0          0          0   _printf_lc.o
        10          0          0          0          0          0   _printf_ll.o
         6          0          0          0          0          0   _printf_lld.o
         6          0          0          0          0          0   _printf_lli.o
         6          0          0          0          0          0   _printf_llo.o
         6          0          0          0          0          0   _printf_llu.o
         6          0          0          0          0          0   _printf_llx.o
       124         16          0          0          0         92   _printf_longlong_dec.o
         6          0          0          0          0          0   _printf_ls.o
         6          0          0          0          0          0   _printf_n.o
         6          0          0          0          0          0   _printf_o.o
       112         10          0          0          0        124   _printf_oct_int_ll.o
         6          0          0          0          0          0   _printf_p.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        36          0          0          0          0         84   _printf_truncate.o
         6          0          0          0          0          0   _printf_u.o
        44          0          0          0          0        108   _printf_wchar.o
       188          6          8          0          0         92   _printf_wctomb.o
         6          0          0          0          0          0   _printf_x.o
        22          0          0          0          0        100   _rserrno.o
       884          4          0          0          0        100   _scanf.o
       332          0          0          0          0         96   _scanf_int.o
        64          0          0          0          0         84   _sgetc.o
        16          0          0          0          0         68   _snputc.o
        10          0          0          0          0         68   _sputc.o
        64          0          0          0          0         92   _wcrtomb.o
        22          0          0          0          0         80   abort.o
       128         42          0          0          0         76   assert.o
        20          0          0          0          0         76   assert_puts.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        48         34          0          0          0         76   defsig_abrt_inner.o
        14          0          0          0          0         80   defsig_abrt_outer.o
        10          0          0          0          0         68   defsig_exit.o
        50          0          0          0          0         88   defsig_general.o
        80         58          0          0          0         76   defsig_rtmem_inner.o
        14          0          0          0          0         80   defsig_rtmem_outer.o
        18          0          0          0          0         80   exit.o
        94          0          0          0          0         80   h1_alloc.o
        52          0          0          0          0         68   h1_extend.o
        78          0          0          0          0         80   h1_free.o
        14          0          0          0          0         84   h1_init.o
         6          0          0          0          0        152   heapauxi.o
         4          0          0          0          0        136   hguard.o
         0          0          0          0          0          0   indicate_semi.o
       138          0          0          0          0        168   init_alloc.o
        18          0          0          0          0         76   isspace.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        42          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
       238          0          0          0          0        100   lludivv7m.o
         0          0          0          0          0          0   maybetermalloc1.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_errno_addr_intlibspace.o
         8          4          0          0          0         68   rt_heap_descriptor_intlibspace.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
        78          0          0          0          0         80   rt_memclr_w.o
       138          0          0          0          0         68   rt_memcpy_v6.o
       100          0          0          0          0         80   rt_memcpy_w.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
        44          8          0          0          0         84   scanf_char.o
       128          0          0          0          0         68   strcmpv7m.o
        12          4          0          0          0         68   sys_exit.o
        74          0          0          0          0         80   sys_stackheap_outer.o
        14          0          0          0          0         76   sys_wrch.o
         2          0          0          0          0         68   use_no_semi.o
        52          4          0          0          0         80   vsnprintf.o
        98          4          0          0          0        140   d2f.o
       688        140          0          0          0        256   ddiv.o
        46          0          0          0          0        116   dflt_clz.o
       340         12          0          0          0        152   dmul.o
       156          4          0          0          0        140   dnaninf.o
        12          0          0          0          0        116   dretinf.o
        86          4          0          0          0        132   f2d.o
       140          4          0          0          0        132   fnaninf.o
        10          0          0          0          0        116   fpinit.o
        10          0          0          0          0        116   fretinf.o
        96          4          0          0          0        124   frnd.o
         4          0          0          0          0        116   printf1.o
         4          0          0          0          0        116   printf2.o
         0          0          0          0          0          0   usenofp.o
       300         42          0          0          0        176   asinf.o
       684         90          0          0          0        208   atan2f.o
        48          0          0          0          0        124   fpclassify.o
        38          0          0          0          0        116   fpclassifyf.o
        80         24          0          0          0        696   funder.o
      1636        110        320          0          0        372   powf.o
       154          0          0          0          0        140   roundf.o
       120          0          0          0          0        272   sqrtf.o

    ----------------------------------------------------------------------
     14376        <USER>        <GROUP>          0         96      10388   Library Totals
        26          0          1          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      9600        428        551          0         96       6512   c_w.l
      1690        172          0          0          0       1772   fz_wm.l
      3060        266        320          0          0       2104   m_wm.l

    ----------------------------------------------------------------------
     14376        <USER>        <GROUP>          0         96      10388   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     40732       3052       4040        380      14116    2790588   Grand Totals
     40732       3052       4040        156      14116    2790588   ELF Image Totals (compressed)
     40732       3052       4040        156          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                44772 (  43.72kB)
    Total RW  Size (RW Data + ZI Data)             14496 (  14.16kB)
    Total ROM Size (Code + RO Data + RW Data)      44928 (  43.88kB)

==============================================================================

