先调通imu使用的串口
调通后，
在回调处理中调用JY901S_ProcessBuffer(&jy901s, uart2_rx_buffer, sizeof(uart2_rx_buffer));
调度器中加入{jy901s_task,100,0},

//测试使用
//uint8_t uart2_flag = 0;

// JY901S陀螺仪实体
JY901S_t jy901s;  

void uart_init(void)
{
	/* 串口环形缓冲区初始化：用于异步处理串口数据 */
  rt_ringbuffer_init(&uart_ringbuffer, ringbuffer_pool, sizeof(ringbuffer_pool));
	/*开启串口DMA空闲中断*/
	HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer));
	__HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);
	
	// 启用串口2 imu专用串口
	HAL_UART_Receive_DMA(&huart2, uart2_rx_buffer, sizeof(uart2_rx_buffer));
	
	JY901S_Create(&jy901s, &huart2, 1000); 
	
//	jy901s_set();
	
//	jy901s_calibration();
}

// 在DMA接收完成回调中处理数据
void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
    if (huart->Instance == USART2)
    {
        // 处理接收到的数据
        JY901S_ProcessBuffer(&jy901s, uart2_rx_buffer, sizeof(uart2_rx_buffer));
				//测试使用
//        uart2_flag = 1;
        // 重新启动DMA接收 收满回调 定长数据
        HAL_UART_Receive_DMA(&huart2, uart2_rx_buffer, sizeof(uart2_rx_buffer));
    }
}

void uart_task(void)
{
	uint16_t length;

	length = rt_ringbuffer_data_len(&uart_ringbuffer);

	if (length != 0)
	{
		rt_ringbuffer_get(&uart_ringbuffer, uart_dma_buffer, length);

		my_printf(&huart1, "uart1:%s\r\n", uart_dma_buffer);
//		my_printf(&huart2, "uart1:%s\r\n", uart_dma_buffer);
		
		// 清空接收缓冲区
		memset(uart_dma_buffer, 0, sizeof(uart_dma_buffer));
	}
//	if(uart2_flag)
//	{
//		uart2_flag = 0;
//		my_printf(&huart1, "uart2:%s\r\n", uart2_rx_buffer);
//		my_printf(&huart2, "uart2:%s\r\n", uart2_rx_buffer);
//		
//		HAL_UART_Receive_DMA(&huart2, uart2_rx_buffer, sizeof(uart2_rx_buffer));
//	}
}