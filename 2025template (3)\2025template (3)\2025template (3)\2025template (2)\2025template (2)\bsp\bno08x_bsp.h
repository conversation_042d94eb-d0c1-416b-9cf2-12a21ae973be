#ifndef __BNO08X_BSP_H__
#define __BNO08X_BSP_H__

#include "bsp_system.h"
#include "i2c.h"

/* BNO08X I2C Address */
#define BNO08X_I2C_ADDR_DEFAULT    0x4A
#define BNO08X_I2C_ADDR_ALT        0x4B

/* BNO08X Hardware Reset Timing */
#define BNO08X_RESET_DELAY_MS      20

/* BNO08X Status Definitions */
typedef enum {
    BNO08X_OK = 0,
    BNO08X_ERROR,
    BNO08X_TIMEOUT,
    BNO08X_BUSY
} BNO08X_Status_t;

/* BNO08X Data Ready Flag */
extern volatile uint8_t bno08x_data_ready;

/* Hardware Abstraction Layer Functions */
void bno08x_bsp_init(void);
BNO08X_Status_t bno08x_bsp_reset(void);
BNO08X_Status_t bno08x_bsp_i2c_write(uint8_t addr, uint8_t *data, uint16_t len);
BNO08X_Status_t bno08x_bsp_i2c_read(uint8_t addr, uint8_t *data, uint16_t len);
uint8_t bno08x_bsp_get_int_status(void);
void bno08x_bsp_delay_ms(uint32_t ms);

/* High-level Interface Functions */
void bno08x_init(void);
void bno08x_proc(void);
BNO08X_Status_t bno08x_check_device(void);

#endif /* __BNO08X_BSP_H__ */
